import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Modal,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { FAB } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';

import MyHeader from '../../components/MyHeader';
import CustomDropDown from '../../components/CustomDropDown';
import CustomInput2 from '../../components/CustomInput2';
import CustomButton from '../../components/CustomButton';
import Colors from '../../styles/Colors';


const feedbackTypes = [
  { label: 'App Experience', value: 'app' },
  { label: 'Bug Report', value: 'bug' },
  { label: 'Feature Request', value: 'feature' },
  { label: 'Other', value: 'other' },
];

// Dummy list (Replace with backend data)
const initialFeedbacks = [
  {
    id: '1',
    type: 'App Experience',
    subject: 'Great App!',
    message: 'The UI is clean and user-friendly.',
    date: '2025-07-10',
  },
  {
    id: '2',
    type: 'Bug Report',
    subject: 'Crashing on start',
    message: 'App crashes on Android 11 after splash screen.',
    date: '2025-07-09',
  },
];

const FeedbackSuggestions = ({ navigation }) => {
    const [feedbacks, setFeedbacks] = useState(initialFeedbacks);
    const [modalVisible, setModalVisible] = useState(false);

    const [type, setType] = useState(null);
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');

    const handleSubmit = () => {
        if (!type || !message.trim()) return;

        const newFeedback = {
            id: (feedbacks.length + 1).toString(),
            type: feedbackTypes.find((t) => t.value === type)?.label || type,
            subject,
            message,
            date: new Date().toISOString().split('T')[0],
        };
        
        setFeedbacks([newFeedback, ...feedbacks]);
        setModalVisible(false);
        setType(null);
        setSubject('');
        setMessage('');
    };

    const renderItem = ({ item }) => (
        <View style={styles.feedbackCard}>
            <View style={styles.feedbackHeader}>
                <Text style={styles.feedbackType}>{item.type}</Text>
                <Text style={styles.feedbackDate}>{item.date}</Text>
            </View>
            {item.subject ? (
                <Text style={styles.feedbackSubject}>📌 {item.subject}</Text>
            ) : null}
            <Text style={styles.feedbackMessage}>{item.message}</Text>
        </View>
    );


  return (
    <View style={styles.screen}>
      <MyHeader title="Feedback & Suggestions" onBackPress={() => navigation.goBack()} />

      <FlatList
        data={feedbacks}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ padding: responsiveWidth(4), paddingBottom: responsiveHeight(10) }}
        ListEmptyComponent={
          <Text style={styles.emptyText}>No feedbacks submitted yet.</Text>
        }
      />

      <FAB
        icon="plus"
        color="white"
        style={styles.fab}
        // onPress={() => setModalVisible(true)}
      />

      {/* Modal Form */}
      <Modal visible={modalVisible} animationType="slide" transparent>
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView contentContainerStyle={styles.modalContent}>
            <Text style={styles.modalTitle}>Write Feedback</Text>

            <CustomDropDown
              uprLabel="Feedback Type"
              iconName="comment-alert-outline"
              value={type}
              setValue={setType}
              data={feedbackTypes}
              placeholder="Select type"
            />

            <CustomInput2
              label="Subject (optional)"
              icon="pencil-outline"
              value={subject}
              onChangeText={setSubject}
              placeholder="Enter subject"
            />

            <CustomInput2
              label="Message"
              icon="message-text-outline"
              value={message}
              onChangeText={setMessage}
              placeholder="Write your feedback..."
              multiline
              numberOfLines={5}
            />

            <CustomButton title="Submit" onPress={handleSubmit} color={Colors.white}  />
            <TouchableOpacity onPress={() => setModalVisible(false)} style={styles.cancelBtn}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </ScrollView>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  )
}

export default FeedbackSuggestions

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  fab: {
    position: 'absolute',
    backgroundColor: Colors.primary,
    right: responsiveWidth(5),
    bottom: responsiveHeight(5),
  },
  feedbackCard: {
    backgroundColor: Colors.primaryWithExtraOpacity,
    borderRadius: responsiveWidth(2),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(1.5),
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: responsiveHeight(0.5),
  },
  feedbackType: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.primary,
  },
  feedbackDate: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
  },
  feedbackSubject: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.black,
    marginBottom: responsiveHeight(0.5),
  },
  feedbackMessage: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.tertiary,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
    marginTop: responsiveHeight(10),
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.white,
    padding: responsiveWidth(5),
    paddingBottom: responsiveHeight(5),
    borderTopLeftRadius: responsiveWidth(5),
    borderTopRightRadius: responsiveWidth(5),
    elevation: 10,
  },
  modalTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(2),
  },
  cancelBtn: {
    marginTop: responsiveHeight(2),
    alignItems: 'center',
  },
  cancelText: {
    color: Colors.error,
    fontSize: responsiveFontSize(1.8),
  },
});