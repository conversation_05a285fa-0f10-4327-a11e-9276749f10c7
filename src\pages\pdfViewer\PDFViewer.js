import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity, Alert, PermissionsAndroid, Platform, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Pdf from 'react-native-pdf';
import MyHeader from '../../components/MyHeader';
import RN<PERSON>lobUtil from 'react-native-blob-util';
import Colors from '../../styles/Colors';
import { responsiveFontSize, responsiveWidth } from 'react-native-responsive-dimensions';

const PDFViewer = ({ route, navigation }) => {
  const { url, pdfName } = route.params;
  const [isDownloading, setIsDownloading] = useState(false);

  const requestAndroidPermission = async () => {
    // On Android 10 (API 29) and above, this permission is not needed for DownloadDir.
    // However, for backward compatibility, it's good to have.
    if (Platform.Version >= 29) {
      return true;
    }
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  };

  const handleDownload = async () => {
    if (isDownloading) return;

    setIsDownloading(true);

    const isPermitted = Platform.OS === 'android'
      ? await requestAndroidPermission()
      : true;

    if (!isPermitted) {
      Alert.alert('Permission Denied', 'Storage permission is required to download the file.');
      setIsDownloading(false);
      return;
    }

    // Use pdfName for a cleaner filename, fallback to parsing the URL
    const filename = pdfName
      ? `${pdfName.replace(/[\s/]/g, '_')}.pdf`
      : url.substring(url.lastIndexOf('/') + 1);

    const dirs = RNBlobUtil.fs.dirs;
    const path = Platform.select({
      ios: `${dirs.DocumentDir}/${filename}`,
      android: `${dirs.DownloadDir}/${filename}`,
    });

    try {
      await RNBlobUtil.config({
        fileCache: true,
        path: path,
        addAndroidDownloads: {
          useDownloadManager: true,
          notification: true,
          path: path,
          description: 'Downloading report...',
          mime: 'application/pdf',
          title: filename,
        },
        // Allow self-signed certs if needed, use with caution
        trusty: true,
      }).fetch('GET', url);

      Alert.alert('Success', `File downloaded to your ${Platform.OS === 'android' ? 'Downloads' : 'Files'} folder.`);
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Error', 'Failed to download the file. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };


  return (
    <View style={{ flex: 1 }}>
      <MyHeader
        title={`${pdfName || 'PDF Viewer'} Report`}
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={handleDownload} style={styles.downloadButton} disabled={isDownloading}>
            {isDownloading ? (
              <ActivityIndicator size="small" color={Colors.primary} />
            ) : (
              <Icon name="download" size={responsiveFontSize(3)} color={Colors.primary} />
            )}
          </TouchableOpacity>
        }
      />

      <Pdf
        source={{ uri: url, cache: true }}
        style={styles.pdf}
        trustAllCerts={false} // Good for security!
        // enablePaging={true}
        onError={(error) => {
          console.error('PDF loading error:', error);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  pdf: {
    flex: 1,
    width: Dimensions.get('window').width,
  },
  downloadButton: {
    marginRight: responsiveWidth(3),
    // padding: responsiveWidth(2),
    // backgroundColor: Colors.white,
    // borderRadius: responsiveWidth(5),
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});

export default PDFViewer;
