import React, { useState } from 'react';
import {
  View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, Image,  KeyboardAvoidingView, Platform
} from 'react-native';
import { RadioButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import CustomButton from '../../components/CustomButton';
import CustomDropDown from '../../components/CustomDropDown';
import MyHeader from '../../components/MyHeader';
import PickImageComponent from '../../components/PickImageComponent';
import CustomInput2 from '../../components/CustomInput2';
import * as DocumentPicker from '@react-native-documents/picker';

const hospitalList = [
  { label: 'Hospital A', value: 'hospitalA' },
  { label: 'Hospital B', value: 'hospitalB' },
];

const specialtyList = [
  { label: 'Cardiology', value: 'cardiology' },
  { label: 'Orthopedics', value: 'orthopedics' },
];

const problemTypes = [
  { label: 'OPD', value: 'OPD' },
  { label: 'IPD', value: 'IPD' },
];

const genderType = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
]

const ageOptions = Array.from({ length: 100 }, (_, i) => ({
  label: `${i + 1}`,
  value: `${i + 1}`,
}));


const ReferPatient = ({ navigation }) => {
    const [hospital, setHospital] = useState(null);
    const [name, setName] = useState('');
    const [age, setAge] = useState('');
    const [sex, setSex] = useState(null);
    const [phone, setPhone] = useState('');
    const [problemType, setProblemType] = useState(null);
    const [specialty, setSpecialty] = useState(null);
    const [remark, setRemark] = useState('');
    const [uploadedDoc, setUploadedDoc] = useState(null);

    // const handleUploadDocument = async () => {
    //     const result = await PickImageComponent();
    //     if (result) {
    //     setUploadedDoc(result);
    //     }
    // };

    const handleUploadDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.images, DocumentPicker.types.pdf],
        allowMultiSelection: false,
      });
      if (res && res.length > 0) {
        setUploadedDoc(res[0]);
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('User cancelled upload');
      } else {
        console.error('Upload Error:', err);
      }
    }
  };

    const handleSubmit = () => {
        console.log({
        hospital,
        name,
        age,
        sex,
        phone,
        problemType,
        specialty,
        uploadedDoc,
        remark,
        });
    };

  return (
    <KeyboardAvoidingView
  style={styles.screen}
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
>
  <MyHeader title="Refer Patient" onBackPress={() => navigation.goBack()} />
  <ScrollView contentContainerStyle={{ paddingBottom: responsiveHeight(5) }}>
    <View style={styles.formWrapper}>
      <Text style={styles.title}>Refer Patient</Text>

      {/* Fields */}
      <View style={styles.field}>
        <CustomDropDown
          title="Select Hospital"
          value={hospital}
          setValue={setHospital}
          data={hospitalList}
          placeholder="Hospital"
          iconName="hospital"
          uprLabel="Hospital"
        />
      </View>

      <View style={styles.field}>
        <CustomInput2 label="Patient Name" value={name} onChangeText={setName} icon="account" placeholder="Patient name" />
      </View>

      <View style={styles.field}>
        <CustomInput2 label="Mobile" value={phone} onChangeText={setPhone} icon="phone" keyboardType="phone-pad" maxLength={10} />
      </View>

      <View style={styles.fieldCmn}>
        <View style={styles.fieldLeft}>
          <CustomDropDown iconName="calendar" value={age} setValue={setAge} data={ageOptions} placeholder="Age" uprLabel="Age" />
        </View>
        <View style={styles.fieldRight}>
          <CustomDropDown iconName="gender-male-female" value={sex} setValue={setSex} data={genderType} placeholder="Gender" uprLabel="Gender" />
        </View>
      </View>

      <View style={styles.field}>
        <CustomDropDown title="Admission Type" value={problemType} setValue={setProblemType} data={problemTypes} placeholder="OPD / IPD" iconName="alert-circle-outline" uprLabel="Admission Type" />
      </View>

      <View style={styles.field}>
        <CustomDropDown title="Specialty" value={specialty} setValue={setSpecialty} data={specialtyList} placeholder="Specialty" iconName="stethoscope" uprLabel="Specialty" />
      </View>

      <View style={styles.field}>
        <CustomInput2 label="Remark" value={remark} onChangeText={setRemark} icon="note-text" placeholder="Enter any remark" />
      </View>

      {/* Upload Section */}
      <View style={styles.field}>
        <Text style={styles.uploadLabel}>Upload Documents</Text>
        <TouchableOpacity style={[styles.uploadBox, uploadedDoc && styles.uploadBoxActive]} onPress={handleUploadDocument}>
          {uploadedDoc?.uri ? (
            uploadedDoc?.type?.includes('image') ? (
              <Image source={{ uri: uploadedDoc.uri }} style={styles.previewImage} />
            ) : (
              <Text style={styles.fileText}>{uploadedDoc.name || 'PDF attached'}</Text>
            )
          ) : (
            <>
              <Icon name="file-upload" size={responsiveFontSize(2.5)} color={Colors.primary} />
              <Text style={styles.uploadText}>Upload Image or PDF</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Submit Button */}
      <View style={styles.submitBtnWrapper}>
        <CustomButton title="Submit" onPress={handleSubmit} color={Colors.white} />
      </View>
    </View>
  </ScrollView>
</KeyboardAvoidingView>

  )
}

export default ReferPatient

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },

  formWrapper: {
    margin: responsiveWidth(4),
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(2),
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },

  title: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: '700',
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: responsiveHeight(1.5),
  },

  field: {
    marginBottom: responsiveHeight(0.5),
  },

  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },

  uploadLabel: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },

  uploadBox: {
    borderWidth: 1,
    borderColor: Colors.primaryWithOpacity,
    borderStyle: 'dashed',
    borderRadius: responsiveWidth(2),
    backgroundColor: '#F0F4F8',
    alignItems: 'center',
    justifyContent: 'center',
    padding: responsiveHeight(2),
    minHeight: responsiveHeight(14),
  },

  uploadBoxActive: {
    backgroundColor: '#EAF6FF',
  },

  previewImage: {
    width: '100%',
    height: responsiveHeight(16),
    borderRadius: responsiveWidth(2),
  },

  uploadText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    marginTop: responsiveHeight(1),
  },

  fileText: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.black,
  },

  submitBtnWrapper: {
    marginTop: responsiveHeight(2.5),
    borderRadius: responsiveWidth(10),
    overflow: 'hidden',
  },

  gradientBtn: {
    paddingVertical: responsiveHeight(1.7),
    alignItems: 'center',
    justifyContent: 'center',
  },

  btnText: {
    fontSize: responsiveFontSize(2),
    fontWeight: '600',
    color: Colors.white,
  },
});
