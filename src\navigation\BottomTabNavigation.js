import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Home from '../pages/home/<USER>';
import Colors from '../styles/Colors';
import Profile from '../pages/profile/Profile';
import BloodBank from '../pages/bloodBank/BloodBank';


const Tab = createBottomTabNavigator();

// Custom tab bar icons and labels
const getTabBarIcon = (route, focused) => {
  let iconName;

  if (route.name === 'Home') {
    iconName = focused ? 'home' : 'home-outline';
  } else if (route.name === 'Blood Bank') {
    iconName = focused ? 'water' : 'water-outline';
  } else if (route.name === 'Transaction') {
    iconName = focused ? 'wallet' : 'wallet-outline';
  } else if (route.name === 'Profile') {
    iconName = focused ? 'account-circle' : 'account-circle-outline';
  }

  return iconName;
};

// Get color for each tab
const getTabColor = (routeName) => {
  switch (routeName) {
    case 'Home':
      return Colors.primary;
    case 'Blood Bank':
      return Colors.primary;
    case 'Transaction':
      return Colors.primary;
    case 'Profile':
      return Colors.primary;
    default:
      return Colors.primary;
  }
};

const BottomTabNavigation = () => {
  return (
    <Tab.Navigator
        screenOptions={({route}) => ({
            headerShown: false,
            tabBarIcon: ({ focused, color, size }) => {
                const iconName = getTabBarIcon(route, focused);
                return (
                    <View style={styles.iconContainer}>
                        <MaterialCommunityIcons name={iconName} size={size} color={color} />
                        {/* {focused && (
                            <View
                            style={[
                                styles.indicator,
                                { backgroundColor: getTabColor(route.name) }
                            ]}
                            />
                        )} */}
                    </View>
                );
            },
            tabBarActiveTintColor: getTabColor(route.name),
            tabBarInactiveTintColor: Colors.tertiary,
            tabBarLabel: ({ focused, color }) => (
                <Text
                    style={[
                    styles.tabLabel,
                    {
                        color,
                        opacity: focused ? 1 : 0.8,
                        fontSize: focused ? responsiveFontSize(1.3) : responsiveFontSize(1.2)
                    }
                    ]}
                >
                    {route.name}
                </Text>
            ),
            tabBarStyle: {
                height: responsiveHeight(6),
                paddingVertical: responsiveHeight(0.5),
                backgroundColor: Colors.white,
                borderTopWidth: 0,
                elevation: 8,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: -2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                borderTopLeftRadius: responsiveWidth(5),
                borderTopRightRadius: responsiveWidth(5),
            },
        })}
    >
        <Tab.Screen name="Home" component={Home} />
        <Tab.Screen name="Blood Bank" component={BloodBank} />
        <Tab.Screen name="Profile" component={Profile} />
    </Tab.Navigator>
  )
}

export default BottomTabNavigation

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: responsiveWidth(12),
    height: responsiveHeight(3),
  },
  indicator: {
    position: 'absolute',
    bottom: responsiveHeight(-1.6),
    width: responsiveWidth(5),
    // height: responsiveHeight(0.4),
    borderRadius: 4,
  },
  tabLabel: {
    fontWeight: '500',
    marginTop: responsiveHeight(-0.3),
    textAlign: 'center',
    fontSize: responsiveFontSize(1),
    color: Colors.tertiary,
  },
});