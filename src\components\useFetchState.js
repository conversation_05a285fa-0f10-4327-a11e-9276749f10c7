import { View, Text, ToastAndroid } from 'react-native'
import React, { useState } from 'react'
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import BaseURL from './BaseURL';

const useFetchState = () => {
    const navigation = useNavigation();
    const [stateList, setStateList] = useState([]);
    const [loading, setLoading] = useState(true);

     const showToast = (msg) => {
        ToastAndroid.showWithGravity(msg, ToastAndroid.TOP, ToastAndroid.SHORT)
    }

     const getStateList = async () => {
        try {
            setLoading(true)
            const baseUrl = await BaseURL.uri;

            const response = await fetch(`${baseUrl}/states`, {
                method: 'GET',
            });

            const result = await response.json();
            if (response.ok) {
                console.log(result)
                const formattedStates = result.data.map(states => ({
                    label: states.name,
                    value: states.id.toString()
                }))
                setStateList(formattedStates)
            } else {
                showToast('Something went wrong');
                console.log('Response error:', result);
            }
        } catch (e) {
            // showToast('Something went wrong');
            console.log('Fetch error:', e);
        } finally {
            setLoading(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            getStateList()
        }, [])
    )

  return { stateList, loading, refetch: getStateList };
}

export default useFetchState