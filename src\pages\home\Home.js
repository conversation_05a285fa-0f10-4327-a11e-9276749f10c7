import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ImageSlider } from '@pembajak/react-native-image-slider-banner';
import { Searchbar } from 'react-native-paper';

const Home = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');

   const quickActions = [
    { label: 'Create Collection', icon: 'account-plus-outline', screen: 'CreateCollectionLead' },
    { label: 'Raise Pickup', icon: 'truck-outline', screen: 'RaisePickupList' },
    // { label: 'Billing Summary', icon: 'file-document-outline', screen: 'BillingSummary' },
    // { label: 'FCS', icon: 'comment-alert-outline', screen: 'FCS' },
  ];

  const modules = [
    { label: 'Collection List', icon: 'clipboard-list-outline', screen: 'Collections', bgColor: Colors.card1 },
    { label: 'Scheduled Collections', icon: 'clipboard-list-outline', screen: 'ScheduledCollections', bgColor: Colors.card2 },
    // { label: 'Price Comparison', icon: 'select-compare', screen: 'PriceComparison', bgColor: Colors.card2 },
    { label: 'Test Ack.', icon: 'flask-outline', screen: 'TestAcknowledge', bgColor: Colors.card3 },
    { label: 'Offers', icon: 'sale', screen: 'OffersPage', bgColor: Colors.card4 },
    { label: 'Refer Patients', icon: 'hospital-box-outline', screen: 'ReferPatientsList', bgColor: Colors.card5 },
    { label: 'Reports', icon: 'file-chart-outline', screen: 'Reports', bgColor: Colors.card6 },
    // { label: 'Profile', icon: 'account-circle-outline', screen: 'Profile', bgColor: Colors.card1 },
    // { label: 'Rewards', icon: 'gift-outline', screen: 'Rewards', bgColor: Colors.card7 },
    // { label: 'Notifications', icon: 'bell-outline', screen: 'Notifications', bgColor: Colors.card8 },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.headerMain}>
          <View style={styles.header}>
              <View style={styles.headerLeft}>
                  <Text style={styles.welcome}>Welcome, Tushar</Text>
                  <Text style={styles.center}>SLD Collection Center</Text>
              </View>
              <TouchableOpacity>
                  <Icon name={'bell-outline'} size={responsiveFontSize(3)} color={Colors.green} />
              </TouchableOpacity>
          </View>

          <Searchbar 
            // clearIcon="close-circle"
            placeholder="Search here"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={styles.searchInput}
            iconColor={Colors.tertiary}
            placeholderTextColor={Colors.tertiary}
          />  
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} >
          <View style={styles.sliderImage}>
              <ImageSlider 
                  data={[
                    { img: 'https://www.dlrgroup.com/media/45_21106_00_N11_weblg-aspect-ratio-1440-850-2140x1263.jpg' },
                    { img: 'https://www.dlrgroup.com/media/45_21106_00_N16_weblg-aspect-ratio-1440-850-1-2140x1264.jpg' },
                    { img: 'https://archello.s3.eu-central-1.amazonaws.com/images/2021/02/09/vebh-architects-butler-health-system-lab-laboratories-archello.**********.055.jpg' },
                  ]}
                  autoPlay={true}
                  preview={false}
                  closeIconColor={Colors.background}
                  caroselImageContainerStyle={styles.imageContainerStyle}
                  caroselImageStyle={styles.imageStyle}
                  // indicatorContainerStyle={styles.indicatorContainer}
                  indicatorContainerStyle={{ display: 'none' }}
              />
          </View>

          <View style={styles.middle}>
              <View style={styles.sectionTitle}><Text style={styles.sectionText}>Quick Actions</Text></View>
              <View style={styles.quickActionContainer}>
                {quickActions.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.quickCard}
                    onPress={() => navigation.navigate(item.screen)}
                    >
                    <Icon name={item.icon} size={responsiveFontSize(4)} color={Colors.primary} />
                    <Text style={styles.quickLabel}>{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
          </View>

          <View style={styles.middle}>
              <View style={styles.sectionTitle}><Text style={styles.sectionText}>Modules</Text></View>
              <View style={styles.moduleContainer}>
                {modules.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.moduleCard, {backgroundColor: item.bgColor}]}
                    onPress={() => navigation.navigate(item.screen)}
                    >
                    <Icon name={item.icon} size={28} color={Colors.primary} />
                    <Text style={styles.moduleLabel}>{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
          </View>
      </ScrollView>
    </View>
  )
}

export default Home


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  headerMain:{
    paddingTop: responsiveHeight(2.5),
    paddingBottom: responsiveHeight(0.5),
    paddingHorizontal: responsiveWidth(3),
  },
  header: {
    flexDirection: 'row',
    alignItems:'center',
    justifyContent:'space-between',
  },
  welcome: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.primary,
    // opacity: 0.8,
  },
  center: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.green,
    fontWeight: 'bold',
  },
 searchBar: {
    marginTop: responsiveHeight(1.5),
    borderRadius: responsiveWidth(6),
    elevation: 2,
    // backgroundColor: Colors.white,
    // height: responsiveHeight(5),
    // borderColor: Colors.primary,
    // borderWidth: 1,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    fontWeight: '600',
    minHeight: responsiveHeight(4),
  },
  content:{
    marginHorizontal: responsiveWidth(3),
  },
  sliderImage:{
    width: responsiveWidth(94),
    height: responsiveHeight(20),
    borderRadius: responsiveWidth(4),
    overflow:'hidden',
    backgroundColor: Colors.primary,
    // marginHorizontal: responsiveWidth(3),
    marginVertical: responsiveHeight(1.5)
  },
  imageContainerStyle:{
    height: responsiveWidth(80), width: responsiveWidth(94), backgroundColor: Colors.primary, 
  },
  imageStyle: {
    height: responsiveHeight(20),
    width: '100%',
    resizeMode: 'stretch',
    overflow:'hidden',
    backgroundColor: 'white', 
  },

  indicatorContainer: {
    bottom: 10,
  },
  middle:{
    // paddingHorizontal: responsiveWidth(3),
  },
  sectionTitle: {
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(1),
  },
  sectionText: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.black,
  },
  quickActionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickCard: {
    width: '48%',
    // backgroundColor: `${Colors.primary}15`,
    padding: responsiveWidth(4),
    borderRadius: responsiveWidth(2),
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
    elevation:4,
    backgroundColor: Colors.white,
  },
  quickLabel: {
    fontSize: responsiveFontSize(1.6),
    marginTop: 5,
    color: Colors.black,
    fontWeight: '500'
  },
  moduleContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  moduleCard: {
    width: '30%',
    // backgroundColor: `${Colors.green}10`,
    padding: responsiveWidth(3),
    borderRadius: responsiveWidth(2),
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
    backgroundColor: '#DDFFFC',
  },
  moduleLabel: {
    fontSize: responsiveFontSize(1.4),
    marginTop: responsiveHeight(1),
    textAlign: 'center',
    color: Colors.black,
    fontWeight: 'bold',
  },
});
