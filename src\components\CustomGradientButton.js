import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../styles/Colors';
const CustomGradientButton = ({ title = 'Share Now', onPress, icon }) => {
  return (
    <TouchableOpacity activeOpacity={0.9} onPress={onPress} style={styles.shareBtnWrapper}>
      <LinearGradient
        colors={['#0072BC', '#005B99']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.shareBtn}
      >
        {icon && 
          <Icon name={icon} size={responsiveFontSize(2.5)} color={Colors.white} />
        }
        <Text style={styles.shareText}>{title}</Text>
      </LinearGradient>
    </TouchableOpacity>
  )
}

export default CustomGradientButton

const styles = StyleSheet.create({
  shareBtnWrapper: {
    marginTop: responsiveHeight(1),
  },
  shareBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: responsiveHeight(1.5),
    borderRadius: responsiveWidth(10),
  },
  shareText: {
    fontSize: responsiveFontSize(2),
    color: Colors.white,
    marginLeft: responsiveWidth(2),
    fontWeight: '600',
  },
});