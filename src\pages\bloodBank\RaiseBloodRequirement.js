import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, KeyboardAvoidingView } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import CustomDropDown from '../../components/CustomDropDown';
import CustomInput2 from '../../components/CustomInput2';
import MyHeader from '../../components/MyHeader';
import CustomButton from '../../components/CustomButton';


const bloodGroups = [
  { label: 'A+', value: 'A+' },
  { label: 'A-', value: 'A-' },
  { label: 'B+', value: 'B+' },
  { label: 'B-', value: 'B-' },
  { label: 'AB+', value: 'AB+' },
  { label: 'AB-', value: 'AB-' },
  { label: 'O+', value: 'O+' },
  { label: 'O-', value: 'O-' },
];

const urgencies = [
  { label: 'High', value: 'High' },
  { label: 'Medium', value: 'Medium' },
  { label: 'Low', value: 'Low' },
];

const RaiseBloodRequirement = ({ navigation }) => {
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [bloodGroup, setBloodGroup] = useState('');
  const [units, setUnits] = useState('');
  const [hospital, setHospital] = useState('');
  const [urgency, setUrgency] = useState('');

  const handleSubmit = () => {
    const payload = {
      name,
      phone,
      bloodGroup,
      units,
      hospital,
      urgency,
    };
    console.log('Submitted data:', payload);

    // TODO: Submit via API
    // You can also add validation here before submitting
  };

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Raise Requirement" onBackPress={() => navigation.goBack()} />
      
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={styles.container}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <CustomInput2
            label="Patient Name"
            placeholder="Enter patient name"
            value={name}
            onChangeText={setName}
            icon="account"
          />

          <CustomInput2
            label="Phone Number"
            placeholder="Enter contact number"
            keyboardType="phone-pad"
            value={phone}
            onChangeText={setPhone}
            icon="phone"
            maxLength={10}
          />
    
          <CustomDropDown
            uprLabel="Blood Group"
            value={bloodGroup}
            setValue={setBloodGroup}
            data={bloodGroups}
            placeholder="Select blood group"
            iconName="blood-bag"
          />

          <CustomInput2
            label="Units Required"
            placeholder="Enter number of units"
            keyboardType="numeric"
            value={units}
            onChangeText={setUnits}
            icon="flask"
          />

          <CustomInput2
            label="Hospital Name"
            placeholder="Enter hospital name"
            value={hospital}
            onChangeText={setHospital}
            icon="hospital-building"
          />

          <CustomDropDown
            uprLabel="Urgency"
            value={urgency}
            setValue={setUrgency}
            data={urgencies}
            placeholder="Select urgency"
            iconName="alert"
          />

          <CustomButton title="Submit Requirement" onPress={handleSubmit} color={Colors.white} />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}

export default RaiseBloodRequirement


const styles = StyleSheet.create({
  container: {
    padding: responsiveWidth(4),
    paddingBottom: responsiveHeight(4),
  },
});