import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

/* ---------------------------------------------------------------- */
/*  Dummy data                       */
/* ---------------------------------------------------------------- */
const transactions = [
  {
    id: '1',
    type: 'credit',
    amount: 550,
    patientName: 'Anki<PERSON>',
    reason: 'CBC Test',
    date: '2025‑07‑10',
    paymentMethod: 'UPI',
    testName: 'CBC',
    testId: 'TEST001',
  },
  {
    id: '2',
    type: 'debit',
    amount: 800,
    patientName: 'Shanskar Varma',
    reason: 'Thyroid Profile',
    date: '2025‑07‑12',
    paymentMethod: 'Card',
    testName: 'Thyroid Profile',
    testId: 'TEST002',
  },
  {
    id: '3',
    type: 'credit',
    amount: 800,
    patientName: 'Kumar Patel',
    reason: 'Thyroid Profile',
    date: '2025‑07‑13',
    paymentMethod: 'Wallet',
    testName: 'Thyroid Profile',
    testId: 'TEST002',
  },
];

/* ---------------------------------------------------------------- */
/*  Constants                                                        */
/* ---------------------------------------------------------------- */
const FILTERS = ['all', 'credit', 'debit'];

const PaymentHistory = ({ navigation }) => {
  const [filter, setFilter] = useState('all');

  /* --------------------------------- */
  /*  derive filtered list with useMemo */
  /* --------------------------------- */
  const filteredTransactions = useMemo(() => {
    if (filter === 'all') return transactions;
    return transactions.filter(t => t.type === filter);
  }, [filter]);

  /* --------------------------------- */
  /*  render functions                  */
  /* --------------------------------- */
  const renderTransaction = ({ item }) => (
    <View style={styles.transactionItem}>
      <Icon
        name={item.type === 'credit' ? 'arrow-down-bold-circle' : 'arrow-up-bold-circle'}
        size={responsiveFontSize(3)}
        color={item.type === 'credit' ? Colors.success : Colors.error}
        style={styles.icon}
      />

      <View style={styles.details}>
        <Text style={styles.date}>{item.type === 'credit' ? 'Received from' : 'Paid to'}</Text>
        <Text style={styles.reason}>{item.patientName}</Text>
        <Text style={styles.date}>{item.date}</Text>
      </View>
        <View>
            <Text style={[styles.amount, { color: item.type === 'credit' ? Colors.success : Colors.danger },]} >
                {item.type === 'credit' ? '+' : '-'} ₹{item.amount}
            </Text>
            <Text style={[styles.date, {textAlign: 'right', textTransform: 'capitalize'}]}>{item.type}</Text>
        </View>
    </View>
  );

  /* --------------------------------- */
  /*  empty‑list component              */
  /* --------------------------------- */
  const Empty = () => (
    <Text style={styles.empty}>
      No {filter === 'all' ? '' : filter === 'credit' ? 'credit ' : 'debit '}
      transactions
    </Text>
  );

  return (
    <View style={styles.container}>
      <MyHeader title="Payment & Balance / History" onBackPress={() => navigation.goBack()} />

      {/* Wallet summary */}
      <View style={styles.balanceBox}>
        <Text style={styles.label}>Wallet Balance</Text>
        <Text style={styles.balance}>₹250</Text>
        <View style={styles.row}>
          <Text style={styles.subInfo}>Credited ₹300</Text>
          <Text style={styles.subInfo}>Debited ₹50</Text>
        </View>
      </View>

      {/* filter chips */}
      <View style={styles.filterTabs}>
        {FILTERS.map(type => (
          <TouchableOpacity
            key={type}
            onPress={() => setFilter(type)}
            style={[styles.tab, filter === type && styles.activeTab,]} >
            <Text style={[ styles.tabText, filter === type && styles.activeTabText,]} >
              {type === 'all' ? 'All' : type === 'credit' ? 'Credited' : 'Debited'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* list */}
      <FlatList
        data={filteredTransactions}
        keyExtractor={item => item.id}
        renderItem={renderTransaction}
        ListEmptyComponent={Empty}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </View>
  );
};

export default PaymentHistory;

/* ---------------------------------------------------------------- */
/*  Styles                                                          */
/* ---------------------------------------------------------------- */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },

  /* ---- Wallet box ---- */
  balanceBox: {
    backgroundColor: Colors.primary,
    padding: responsiveHeight(2.5),
    borderRadius: 10,
    margin: responsiveHeight(2),
    elevation: 4,
  },
  label: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.8),
  },
  balance: {
    fontSize: responsiveFontSize(3.5),
    color: Colors.white,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  subInfo: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.55),
  },

  /* ---- Filter tabs ---- */
  filterTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: responsiveHeight(1),
    paddingHorizontal: responsiveWidth(4),
  },
  tab: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.lightGray,
  },
  activeTab: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  tabText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.darkGray,
  },
  activeTabText: {
    color: Colors.white,
    fontWeight: '600',
  },

  /* ---- List item ---- */
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: responsiveHeight(1.5),
    borderBottomWidth: 0.6,
    borderColor: Colors.lightGray,
    paddingHorizontal: responsiveWidth(5),
  },
  icon: { marginRight: 12 },
  details: { flex: 1 },
  reason: { fontSize: responsiveFontSize(2), color: Colors.black },
  testName: { fontSize: responsiveFontSize(1.6), color: Colors.darkGray },
  meta: { fontSize: responsiveFontSize(1.4), color: Colors.gray },
  date: { fontSize: responsiveFontSize(1.4), color: Colors.gray },
  amount: { fontSize: responsiveFontSize(2), fontWeight: 'bold' },

  /* ---- Empty state ---- */
  empty: {
    textAlign: 'center',
    marginTop: responsiveHeight(3),
    color: Colors.tertiary,
    fontSize: responsiveFontSize(1.7),
  },
});
