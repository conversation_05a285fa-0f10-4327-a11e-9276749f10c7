import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  ToastAndroid,
  Platform,
} from 'react-native';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Clipboard from '@react-native-clipboard/clipboard';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import MyHeader from '../../components/MyHeader';
import Colors from '../../styles/Colors';
import CustomGradientButton from '../../components/CustomGradientButton';

const ReferApp = ({ navigation }) => {
    const referralCode = 'ABC123';
    const totalReferrals = 8;
    const totalPoints = 1050;

     /* -------- helpers -------- */
    const copyCode = () => {
        Clipboard.setString(referralCode);
        ToastAndroid.show('Code copied!', ToastAndroid.SHORT);
    };

    const shareApp = async () => {
        try {
        await Share.share({
            message: `Use my referral code ${referralCode} to sign‑up and get free rewards!\nDownload the app: https://yourapp.link/download`,
        });
        } catch (err) {
        console.warn(err);
        }
    };

  return (
    <View style={styles.screen}>
      <MyHeader title="Refer & Earn" onBackPress={() => navigation.goBack()} />

      {/* 1. Referral Code Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Your Referral Code</Text>
        <TouchableOpacity style={styles.codeBox} onPress={copyCode}>
          <Text style={styles.codeText}>{referralCode}</Text>
          <Icon name="content-copy" size={responsiveFontSize(2.2)} color={Colors.primary} />
        </TouchableOpacity>

        {/* 2. Stats */}
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Icon name="account-multiple" size={responsiveFontSize(3)} color={Colors.primary} />
            <Text style={styles.statValue}>{totalReferrals}</Text>
            <Text style={styles.statLabel}>Referrals</Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="star-circle" size={responsiveFontSize(3)} color={Colors.primary} />
            <Text style={styles.statValue}>{totalPoints}</Text>
            <Text style={styles.statLabel}>Points</Text>
          </View>
        </View>

        {/* 3. Share Button */}
        <CustomGradientButton title="Share Now" onPress={shareApp} icon='share-variant' />
      </View>

      {/* 4. How It Works */}
      <View style={styles.howItWorks}>
        <Text style={styles.howTitle}>How it works</Text>
        <View style={styles.howStep}>
          <Icon name="numeric-1-circle" size={responsiveFontSize(3)} color={Colors.primary} />
          <Text style={styles.howText}>Share your code with friends.</Text>
        </View>
        <View style={styles.howStep}>
          <Icon name="numeric-2-circle" size={responsiveFontSize(3)} color={Colors.primary} />
          <Text style={styles.howText}>They sign‑up & book any test.</Text>
        </View>
        <View style={styles.howStep}>
          <Icon name="numeric-3-circle" size={responsiveFontSize(3)} color={Colors.primary} />
          <Text style={styles.howText}>You both earn reward points.</Text>
        </View>
      </View>
    </View>
  )
}

export default ReferApp


/* ---------------- styles ---------------- */
const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  card: {
    margin: responsiveWidth(4),
    backgroundColor: Colors.white,
    padding: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    elevation: 3,
  },
  cardTitle: {
    fontSize: responsiveFontSize(2),
    color: Colors.tertiary,
    marginBottom: responsiveHeight(1.5),
  },
  codeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: responsiveWidth(2),
    paddingVertical: responsiveHeight(1.2),
    paddingHorizontal: responsiveWidth(3),
  },
  codeText: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    color: Colors.primary,
    letterSpacing: 2,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: responsiveHeight(2),
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  statLabel: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
  },

  howItWorks: {
    marginHorizontal: responsiveWidth(4),
    marginTop: responsiveHeight(1),
  },
  howTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: responsiveHeight(1),
  },
  howStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(0.8),
  },
  howText: {
    marginLeft: responsiveWidth(2),
    fontSize: responsiveFontSize(1.7),
    color: Colors.black,
    flexShrink: 1,
  },
});