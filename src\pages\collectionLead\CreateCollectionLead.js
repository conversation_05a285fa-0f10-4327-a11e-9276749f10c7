import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, KeyboardAvoidingView } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MyHeader from '../../components/MyHeader';
import CustomInput2 from '../../components/CustomInput2';
import CustomDateTimePicker from '../../components/CustomDateTimePicker';
import CustomButton from '../../components/CustomButton';
import Colors from '../../styles/Colors';
import CustomDropDown from '../../components/CustomDropDown';
import CustomMultipleDropdown from '../../components/CustomMultipleDropdown';
import { Platform } from 'react-native';

const genderOptions = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
    { label: 'Other', value: 'Other' },
];

const testOptions = [
  { label: 'Blood Test', value: 'blood', price: 200 },
  { label: 'Urine Test', value: 'urine', price: 150 },
  { label: 'X-Ray', value: 'xray', price: 500 },
  { label: 'COVID Test', value: 'covid', price: 300 },
  { label: 'MRI Scan', value: 'mri', price: 1500 },
  { label: 'CBC Test', value: 'CBC', price: 250 },
];

const ageOptions = Array.from({ length: 100 }, (_, i) => ({
  label: `${i + 1}`,
  value: `${i + 1}`,
}));


const labOptions = [
  { label: 'Lab A', value: 'labA' },
  { label: 'Lab B', value: 'labB' },
  { label: 'Lab C', value: 'labC' },
];

const collectionTypeList = [
  { label: 'Draw', value: 'Draw' },
  { label: 'Pickup', value: 'Pickup' },
  { label: 'Draw & Pickup', value: 'Draw & Pickup' },
];

const paymentModeOptions = [
  { label: 'Cash', value: 'cash' },
  { label: 'Card', value: 'card' },
  { label: 'UPI', value: 'upi' },
  { label: 'Net Banking', value: 'netbanking' },
];


const CreateCollectionLead = ({ navigation, route }) => {
    const { item, isEditMode } = route.params || {item: {}, isEditMode: false};
    const [name, setName] = useState('');
    const [age, setAge] = useState('');
    const [sex, setSex] = useState('');
    const [contact, setContact] = useState('');
    const [address, setAddress] = useState('');
    const [collectionType, setCollectionType] = useState('');
    const [selectedDate, setSelectedDate] = useState(null);
    const [selectedTests, setSelectedTests] = useState([]);
    const [paymentAmount, setPaymentAmount] = useState('');
    const [labName, setLabName] = useState('');
    const [paymentMode, setPaymentMode] = useState('');

    useEffect(() => {
        console.log(item)
        if (isEditMode && item) {
            setName(item.name || '');
            setAge(item.age?.toString() || '');
            setSex(item.gender || ''); // Depending on whether you use 'sex' or 'gender'
            setContact(item.phone || '');
            setAddress(item.address || '');
            if (item.date) {
                const [day, month, year] = item.date.split('-');
                const parsedDate = new Date(`${year}-${month}-${day}`); // yyyy-MM-dd
                setSelectedDate(parsedDate);
            }
            setLabName(item.labName || '');
            setCollectionType(item.collectionType || '')
            // setPaymentMode(item.paymentMode || '');

            // map test names to values in testOptions
            const testValues = (item.testNames || []).map(test => {
            const match = testOptions.find(t => t.label === test.name);
            return match?.value;
            }).filter(Boolean);
            setSelectedTests(testValues);
        }
    }, []);



    useEffect(() => {
        const selectedTestDetails = selectedTests.map(testValue => {
            const test = testOptions.find(t => t.value === testValue);
            return { name: test?.label, price: test?.price || 0 };
        });

        const totalPrice = selectedTestDetails.reduce((sum, item) => sum + item.price, 0);
        setPaymentAmount(totalPrice.toString());
    }, [selectedTests]);

    console.log(selectedTests)
    const handleCreateLead = () => {
        const selectedTestDetails = selectedTests.map(testValue => {
            const test = testOptions.find(t => t.value === testValue);
            return { name: test?.label, price: test?.price };
        });

        const totalPrice = selectedTestDetails.reduce((sum, item) => sum + (item.price || 0), 0);
        const payload = {
            name,
            age,
            sex,
            contact,
            address,
            selectedDate,
            collectionType,
            labName,
            selectedTests: selectedTestDetails,
            totalPrice,
            // paymentAmount,
            // paymentMode, 
        };
        console.log('Create Collection Payload:', payload);
        navigation.goBack()
    };


  return (
    <KeyboardAvoidingView style={{flex:1}}  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <MyHeader title={isEditMode ? 'Update Collection/Lead' : 'Create Collection/Lead'} onBackPress={() => navigation.goBack()} />
        <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
            {/* <Text style={styles.heading}>Create Collection / Lead</Text> */}
            <View style={styles.section}>
                <Text style={styles.sectionLabel}>Patient Details</Text>
                <CustomInput2
                    label="Name"
                    value={name}
                    onChangeText={setName}
                    icon="account"
                    placeholder="Enter name"
                />
                {/* <CustomInput2
                    label="Age"
                    value={age}
                    onChangeText={setAge}
                    icon="calendar-account"
                    placeholder="Enter age"
                    keyboardType="numeric"
                />
                <CustomDropDown
                    title="Gender"
                    value={sex}
                    setValue={setSex}
                    iconName="gender-male-female"
                    placeholder="Select gender"
                    data={genderOptions}
                    uprLabel={'Gender'}
                /> */}

                <View style={styles.fieldCmn}>
                    <View style={styles.fieldLeft}>
                        <CustomDropDown iconName="calendar" value={age} setValue={setAge} data={ageOptions} placeholder="Age" uprLabel="Age" />
                    </View>
                    <View style={styles.fieldRight}>
                        <CustomDropDown iconName="gender-male-female" value={sex} setValue={setSex} data={genderOptions} placeholder="Gender" uprLabel="Gender" />
                    </View>
                </View>


                <CustomInput2
                    label="Mobile"
                    value={contact}
                    onChangeText={setContact}
                    icon="phone"
                    keyboardType="phone-pad"
                    maxLength={10}
                />
                <CustomInput2
                    label="Address"
                    value={address}
                    onChangeText={setAddress}
                    icon="map-marker"
                    placeholder="Enter address"
                />
            </View>
            <View style={styles.section}>
                <Text style={styles.sectionLabel}>Collection Type</Text>
                <CustomDropDown
                    value={collectionType}
                    setValue={setCollectionType}
                    iconName="needle"
                    placeholder="Type"
                    data={collectionTypeList}
                    uprLabel={'Type'}
                />
            </View>
            <View style={styles.section}>
                <Text style={styles.sectionLabel}>Collection Schedule</Text>
                <CustomDateTimePicker
                    title="Collection Date"
                    placeholder="Date"
                    value={selectedDate}
                    setDate={setSelectedDate}
                    iconName="calendar"
                    mode="date"
                />
            </View>
            <View style={styles.section}>
                <Text style={styles.sectionLabel}>Test Details</Text>
                <CustomMultipleDropdown 
                    iconName="flask-outline"
                    placeholder="Test(s)"
                    data={testOptions}
                    value={selectedTests}
                    setValue={setSelectedTests}
                    uprLabel={'Test'}
                    dropdownPosition='top'
                />
            </View>
            {selectedTests.length > 0 && (
                <View style={{ marginBottom: responsiveHeight(1), }}>
                    {selectedTests.map((selected) => {
                    const test = testOptions.find((t) => t.value === selected);
                    return (
                        <Text key={selected} style={styles.testItem}>
                        {test?.label} - ₹{test?.price}
                        </Text>
                    );
                    })}
                </View>
            )}
            {/* <View style={styles.section}>
                <Text style={styles.sectionLabel}>Payment</Text>
                <CustomInput2
                    label="Amount"
                    value={paymentAmount}
                    onChangeText={setPaymentAmount}
                    icon="cash"
                    placeholder="Enter amount"
                    keyboardType="numeric"
                />
                <CustomDropDown
                    title="Payment Mode"
                    value={paymentMode}
                    setValue={setPaymentMode}
                    iconName="credit-card"
                    placeholder="Select payment mode"
                    data={paymentModeOptions}
                />
            </View> */}

            <CustomButton title={isEditMode ? 'Update Collection' : 'Create Collection'} color={Colors.white} onPress={handleCreateLead} />
        </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default CreateCollectionLead


const styles = StyleSheet.create({
  container: {
    // flex: 1,
    paddingHorizontal: responsiveWidth(4),
    backgroundColor: Colors.white,
  },
  heading: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(2),
  },
  section:{
    // marginBottom: responsiveHeight(1),
  },
  sectionLabel: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '500',
    color: Colors.black,
    marginBottom: responsiveHeight(1.5),
  },
  testItem: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.black,
    // marginVertical: 2,
  },

  fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },
});