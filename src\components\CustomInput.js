import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import Colors from '../styles/Colors'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'

const CustomInput = ({iconName, placeholder, title, value, onChangeText, eye = false, keyboardType = 'default', maxLength, editable = true, secureTextEntry = false, rightIcon = null, onRightIconPress = null, errorMessage = '', gap = 0, ...props}) => {
    const [showPassword, setShowPassword] = useState(secureTextEntry);
  return (
    <View style={[styles.main, {gap: gap}]}>
      <Text style={styles.label}>{title}</Text>
      <View style={styles.inputContainer}>
        <View style={styles.icon}>
          <MaterialCommunityIcons name={iconName} size={responsiveFontSize(2.5)} color={Colors.primary} />
        </View>
        <TextInput
          style={[
            styles.input,
            !editable && styles.disabledInput
          ]}
          placeholder={placeholder}
          placeholderTextColor={'grey'}
          value={value}
          onChangeText={onChangeText}
          keyboardType={keyboardType}
          maxLength={maxLength}
          editable={editable}
          secureTextEntry={eye ? showPassword : secureTextEntry}
          {...props}
        />
        {eye && (
          <TouchableOpacity style={styles.EyeIcon} onPress={() => setShowPassword(prev => !prev)}>
            <MaterialCommunityIcons
              name={showPassword ? 'eye-off' : 'eye'}
              size={responsiveFontSize(2.5)}
              color={Colors.primary}
            />
          </TouchableOpacity>
        )}
        {rightIcon && (
          <TouchableOpacity style={styles.EyeIcon} onPress={onRightIconPress}>
            <MaterialCommunityIcons
              name={rightIcon}
              size={responsiveFontSize(2.5)}
              color={Colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>
      {errorMessage ? <Text style={styles.errorText}>{errorMessage}</Text> : null}
    </View>
  )
}

export default CustomInput

const styles = StyleSheet.create({
  main: {
    width:'100%',
    // paddingHorizontal: responsiveWidth(3),
    // paddingVertical: responsiveHeight(1),
    gap: responsiveWidth(1),
  },
 label: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
  },
  inputContainer: {
    width: '100%',
    borderColor: Colors.primaryWithOpacity,
    borderWidth: 0.5,
    borderRadius: responsiveWidth(2),
    flexDirection: 'row',
    alignItems: 'center',
    // paddingLeft: responsiveWidth(2),
    overflow:'hidden'
  },
  icon:{
      backgroundColor:Colors.primaryWithOpacity,
      padding:responsiveWidth(2),
  },
  input: {
    flex:1,
    // borderColor: Colors.tertiary,
    // borderWidth: 0.5,
    paddingHorizontal: responsiveWidth(1.5),
    paddingVertical: responsiveWidth(2),
    borderRadius: responsiveWidth(2),
    fontSize: responsiveFontSize(1.8),
    color: Colors.black,
    marginLeft: responsiveWidth(1),
  },
  disabledInput: {
    backgroundColor: Colors.primaryWithExtraOpacity,
    color: Colors.primary,
    fontWeight: '500',
  },
  EyeIcon:{
    // backgroundColor:Colors.primaryWithOpacity,
    padding:responsiveWidth(2),
  },
  errorText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.error,
    marginTop: responsiveHeight(0.5),
  },
})