import React, { useState, useMemo } from 'react';
import {
  View, Text, StyleSheet, FlatList, TouchableOpacity
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import moment from 'moment';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

// Sample billing data without "period"
const billingData = [
  { id: '1',  type: 'debit',  patient: 'Ankit Varma',    test: 'CBC',              amount: 550, date: '2025-07-13' },
  { id: '2',  type: 'debit',  patient: '<PERSON><PERSON>',    test: 'Thyroid',          amount: 850, date: '2025-07-11' },
  { id: '3',  type: 'credit', patient: 'Refund',         test: 'Thyroid',          amount: 850, date: '2025-07-10' },
  { id: '4',  type: 'debit',  patient: '<PERSON><PERSON>',    test: 'Lipid Profile',    amount: 900, date: '2025-07-08' },
  { id: '5',  type: 'debit',  patient: '<PERSON>',    test: '<PERSON>min D',        amount: 600, date: '2025-07-05' },
  { id: '6',  type: 'debit',  patient: 'Anjali Roy',     test: 'Blood Sugar',      amount: 400, date: '2025-07-01' },
  { id: '7',  type: 'credit', patient: 'Refund',         test: 'Vitamin D',        amount: 600, date: '2025-06-28' },
  { id: '8',  type: 'debit',  patient: 'Sumit Rana',     test: 'Kidney Function',  amount: 1100, date: '2025-06-20' },
  { id: '9',  type: 'debit',  patient: 'Priya Jha',      test: 'Liver Function',   amount: 1300, date: '2025-06-15' },
  { id: '10', type: 'debit',  patient: 'Ravi Sharma',    test: 'CBC',              amount: 550, date: '2025-06-03' },
  { id: '11', type: 'debit',  patient: 'Deepika Singh',  test: 'HbA1c',            amount: 700, date: '2025-05-28' },
  { id: '12', type: 'credit', patient: 'Refund',         test: 'Liver Function',   amount: 1300, date: '2025-05-25' },
  { id: '13', type: 'debit',  patient: 'Amit Jain',      test: 'Iron Test',        amount: 450, date: '2025-05-12' },
  { id: '14', type: 'debit',  patient: 'Sneha Kapoor',   test: 'CRP',              amount: 750, date: '2025-04-20' },
  { id: '15', type: 'debit',  patient: 'Tina Dey',       test: 'Thyroid Profile',  amount: 850, date: '2025-04-01' },
  { id: '16', type: 'credit', patient: 'Refund',         test: 'CRP',              amount: 750, date: '2025-03-30' },
  { id: '17', type: 'debit',  patient: 'Jatin Malik',    test: 'CBC',              amount: 550, date: '2025-03-15' },
  { id: '18', type: 'debit',  patient: 'Zoya Sheikh',    test: 'Lipid Profile',    amount: 950, date: '2025-02-10' },
  { id: '19', type: 'credit', patient: 'Refund',         test: 'CBC',              amount: 550, date: '2025-01-25' },
  { id: '20', type: 'debit',  patient: 'Rohit Bansal',   test: 'Thyroid',          amount: 850, date: '2025-01-12' },
];


// Determine period dynamically
const getPeriod = (dateStr) => {
  const today = moment();
  const date = moment(dateStr);

  if (today.diff(date, 'days') <= 7) return 'weekly';
  if (today.isSame(date, 'month')) return 'monthly';
  if (today.isSame(date, 'year')) return 'yearly';
  return 'older';
};

const BillingSummary = ({ navigation }) => {
  const [period, setPeriod] = useState('weekly');

  const filteredBilling = useMemo(() => {
    return billingData.filter(item => getPeriod(item.date) === period);
  }, [period]);

  const renderBillingItem = ({ item }) => (
    <View style={styles.billingItem}>
      <View style={{ flex: 1 }}>
        <Text style={styles.patient}>{item.patient}</Text>
        <Text style={styles.test}>🧪 {item.test}</Text>
        <Text style={styles.date}>{item.date}</Text>
      </View>
      <View>
        <Text style={[
          styles.amount,
          { color: item.type === 'credit' ? Colors.success : Colors.danger }
        ]}>
          {item.type === 'credit' ? '+' : '-'} ₹{item.amount}
        </Text>
        <Text style={styles.type}>{item.type}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <MyHeader title="Billing Summary" onBackPress={() => navigation.goBack()} />

      <View style={styles.periodTabs}>
        {['weekly', 'monthly', 'yearly'].map(p => (
          <TouchableOpacity
            key={p}
            onPress={() => setPeriod(p)}
            style={[styles.tab, period === p && styles.activeTab]}
          >
            <Text style={[styles.tabText, period === p && styles.activeTabText]}>
              {p.charAt(0).toUpperCase() + p.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={styles.sectionTitle}>Test Billing Details</Text>
      <FlatList
        data={filteredBilling}
        renderItem={renderBillingItem}
        keyExtractor={item => item.id}
        ListEmptyComponent={<Text style={styles.empty}>No records found.</Text>}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </View>
  );
};

export default BillingSummary;

// Styles remain the same as your current version

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  balanceBox: {
    backgroundColor: Colors.primary,
    padding: responsiveHeight(2.5),
    borderRadius: 10,
    margin: responsiveHeight(2),
    elevation: 4,
  },
  label: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.8),
  },
  balance: {
    fontSize: responsiveFontSize(3.5),
    color: Colors.white,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  subInfo: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.6),
  },
  periodTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: responsiveHeight(1),
    paddingHorizontal: responsiveWidth(4),
  },
  tab: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.lightGray,
  },
  activeTab: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  tabText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.darkGray,
  },
  activeTabText: {
    color: Colors.white,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.black,
    marginHorizontal: responsiveWidth(5),
    marginBottom: responsiveHeight(1),
  },
  billingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: responsiveHeight(1.5),
    borderBottomWidth: 0.6,
    borderColor: Colors.lightGray,
    marginHorizontal: responsiveWidth(5),
  },
  patient: {
    fontSize: responsiveFontSize(1.9),
    color: Colors.black,
  },
  test: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.darkGray,
  },
  date: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.gray,
  },
  amount: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    textAlign: 'right',
  },
  type: {
    fontSize: responsiveFontSize(1.5),
    textTransform: 'capitalize',
    color: Colors.tertiary,
    textAlign: 'right',
  },
  empty: {
    textAlign: 'center',
    marginTop: responsiveHeight(3),
    color: Colors.tertiary,
    fontSize: responsiveFontSize(1.6),
  },
});