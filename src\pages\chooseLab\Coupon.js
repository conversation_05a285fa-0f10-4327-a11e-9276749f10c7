import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, FlatList, Alert, ToastAndroid } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MyHeader from '../../components/MyHeader';

const Coupon = ({ navigation }) => {
  const [couponCode, setCouponCode] = useState('');
 const [availableCoupons, setAvailableCoupons] = useState([
  { id: '1', code: 'SAVE10', description: 'Get 10% off on your first order', validTill: '2025-08-15' },
  { id: '2', code: 'FREESHIP', description: 'Free shipping on orders above ₹500', validTill: '2025-08-31' },
  { id: '3', code: 'WELCOME50', description: '₹50 off on your first booking', validTill: '2025-09-10' },
]);


  const applyCoupon = () => {
    if (couponCode.trim() === '') {
      showToast('Please enter a coupon code');
      return;
    }
    const found = availableCoupons.find(c => c.code.toLowerCase() === couponCode.toLowerCase());
    if (found) {
      showToast(`Coupon "${found.code}" applied!`);
      navigation.goBack(); // Or send couponCode to previous screen using params
    } else {
      showToast('Invalid coupon code');
    }
  };

  const handleSelectCoupon = (code) => {
    setCouponCode(code);
  };

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.SHORT, ToastAndroid.CENTER);
  }

  return (
    <View style={{ flex: 1 }}>
      <MyHeader title="Coupons" onBackPress={() => navigation.goBack()} />
    <View style={styles.container}>      
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Enter Coupon Code</Text>
        <View style={styles.inputRow}>
          <TextInput
            placeholder="E.g. SAVE10"
            value={couponCode}
            onChangeText={setCouponCode}
            style={styles.input}
          />
          <TouchableOpacity style={styles.applyButton} onPress={applyCoupon}>
            <Text style={styles.applyText}>Apply</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Text style={styles.label}>Available Offers</Text>
      <FlatList
        data={availableCoupons}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.couponCard,{ borderWidth: 1, borderColor: couponCode === item.code ? Colors.primary : Colors.background }]}
            onPress={() => handleSelectCoupon(item.code)}
          >
            <View style={styles.cardLeft}>
              <Icon name="ticket-percent" size={responsiveFontSize(3)} color={Colors.primary} />
            </View>
            <View style={styles.cardRight}>
              <Text style={styles.couponCode}>{item.code}</Text>
              <Text style={styles.description}>{item.description}</Text>
              <Text style={styles.validTill}>Valid Till: {item.validTill}</Text>
            </View>
          </TouchableOpacity>
        )}
        contentContainerStyle={{ paddingBottom: responsiveHeight(2) }}
      />
    </View>
    </View>
  );
};

export default Coupon;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: responsiveWidth(4),
  },
  inputContainer: {
    marginBottom: responsiveHeight(2),
  },
  label: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    marginBottom: responsiveHeight(1),
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: responsiveWidth(2),
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    padding: responsiveWidth(3),
    fontSize: responsiveFontSize(2),
    color: Colors.black,
    fontWeight: '600',
  },
  applyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(1.5),
  },
  applyText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: responsiveFontSize(1.8),
  },
  couponCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: responsiveWidth(3),
    borderRadius: responsiveWidth(1),
    marginBottom: responsiveHeight(1.5),
    elevation: 2,
  },
  cardLeft: {
    marginRight: responsiveWidth(3),
  },
  cardRight: {
    flex: 1,
  },
  couponCode: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  description: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.black,
    marginTop: 2,
  },
  validTill: {
  fontSize: responsiveFontSize(1.4),
  color: Colors.black,
  marginTop: 4,
},

});
