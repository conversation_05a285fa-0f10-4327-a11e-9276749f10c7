import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

const tabs = [
    { key: 'Earned', icon: 'trophy-outline', label: 'Earned' },
    { key: 'Redeemed', icon: 'gift-outline', label: 'Redeemed' },
    { key: 'History', icon: 'history', label: 'History' },
  ];

  const earnedData = [
  { id: '1', user: '<PERSON><PERSON>', action: 'Referred', points: '+100 pts', date: '2025-07-10' },
  { id: '2', user: '<PERSON><PERSON>', action: 'Booked CBC Test', points: '+50 pts', date: '2025-07-11' },
  { id: '3', user: '<PERSON><PERSON>', action: 'Referred', points: '+100 pts', date: '2025-07-09' },
  { id: '4', user: '<PERSON><PERSON>', action: 'Booked Thyroid Test', points: '+50 pts', date: '2025-07-10' },
];

const redeemedData = [
  { id: '1', item: 'Digital Thermometer', points: '-500 pts', date: '2025-07-11' },
  { id: '2', item: '₹100 Amazon Voucher', points: '-1000 pts', date: '2025-07-10' },
  { id: '3', item: 'Free Diabetes Consultation', points: '-800 pts', date: '2025-07-08' },
];


const historyData = [
  { id: '1', text: 'You earned 100 pts for referring Aman', date: '2025-07-10' },
  { id: '2', text: 'You earned 50 pts when Aman booked CBC', date: '2025-07-11' },
  { id: '3', text: 'You spent 1000 pts for Amazon Voucher', date: '2025-07-08' },
];



const Rewards = ({ navigation }) => {
    const [selectedTab, setSelectedTab] = useState('Earned');

    const renderEarnedItem = ({ item }) => (
  <View style={styles.rewardItem}>
    <View>
      <Text style={styles.rewardText}>👤 {item.user}</Text>
      <Text style={styles.rewardSub}>{item.action}</Text>
      <Text style={styles.rewardDate}>{item.date}</Text>
    </View>
    <Text style={styles.rewardPoints}>{item.points}</Text>
  </View>
);


  const renderRedeemedItem = ({ item }) => (
  <View style={styles.rewardItem}>
    <View>
      <Text style={styles.rewardText}>🎁 {item.item}</Text>
      <Text style={styles.rewardDate}>{item.date}</Text>
    </View>
    <Text style={[styles.rewardPoints, { color: Colors.error }]}>{item.points}</Text>
  </View>
);



 const renderHistoryItem = ({ item }) => (
  <View style={styles.rewardItem}>
    <View>
      <Text style={styles.rewardText}>🕒 {item.text}</Text>
      <Text style={styles.rewardDate}>{item.date}</Text>
    </View>
  </View>
);


  const renderTabContent = () => {
    switch (selectedTab) {
      case 'Earned': return <FlatList data={earnedData} renderItem={renderEarnedItem} keyExtractor={item => item.id} />;
      case 'Redeemed': return <FlatList data={redeemedData} renderItem={renderRedeemedItem} keyExtractor={item => item.id} />;
      case 'History': return <FlatList data={historyData} renderItem={renderHistoryItem} keyExtractor={item => item.id} />;
    }
  };

  return (
    <View style={styles.container}>
        <MyHeader title="Rewards" onBackPress={() => navigation.goBack()} />
        <View style={{paddingHorizontal: responsiveWidth(4), flex: 1,}}>
            <View style={styles.pointsBox}>
                <Text style={styles.totalPointsLabel}>Total Reward Points</Text>
                <Text style={styles.totalPoints}>5600</Text>
            </View>

            <View style={styles.tabBar}>
                {tabs.map(tab => (
                    <TouchableOpacity
                        key={tab.key}
                        style={[styles.tabButton, selectedTab === tab.key && styles.tabButtonActive]}
                        onPress={() => setSelectedTab(tab.key)}
                    >
                        <Icon name={tab.icon} size={responsiveFontSize(2.3)} color={selectedTab === tab.key ? Colors.primary : Colors.tertiary} />
                        <Text style={[styles.tabLabel, selectedTab === tab.key && styles.tabLabelActive]}>
                        {tab.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            <View style={styles.contentArea}>
                {renderTabContent()}
            </View>
        </View>
    </View>
  )
}

export default Rewards

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    // padding: responsiveWidth(4),
  },
  pointsBox: {
    alignItems: 'center',
    marginVertical: responsiveHeight(2),
  },
  totalPointsLabel: {
    fontSize: responsiveFontSize(2),
    color: Colors.tertiary,
  },
  totalPoints: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.primaryWithExtraOpacity,
    borderRadius: responsiveWidth(2),
    overflow: 'hidden',
    marginBottom: responsiveHeight(2),
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: responsiveHeight(1),
  },
  tabButtonActive: {
    backgroundColor: Colors.primaryWithOpacity,
  },
  tabLabel: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
    marginTop: responsiveHeight(0.5),
  },
  tabLabelActive: {
    color: Colors.primary,
    fontWeight: 'bold',
  },
  contentArea: {
    flex: 1,
  },
  rewardItem: {
    backgroundColor: `${Colors.pink}10` || '#f9f9f9',
    borderRadius: responsiveWidth(2),
    padding: responsiveHeight(1.5),
    marginBottom: responsiveHeight(1.2),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rewardText: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.black,
  },
  rewardSub: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
  },
  rewardPoints: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.green,
  },
  rewardDate: {
  fontSize: responsiveFontSize(1.4),
  color: Colors.tertiary,
  marginTop: responsiveHeight(0.3),
},

});