import React from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import Login from '../pages/login/Login'
import BottomTabNavigation from './BottomTabNavigation'
import Login2 from '../pages/login/Login2'
import ForgotPassword from '../pages/forgotPassword/ForgotPassword'
import OtpVerification from '../pages/forgotPassword/OtpVerification'
import ProfileManagement from '../pages/profile/pages/ProfileManagement'
import ChangePassword from '../pages/profile/pages/ChangePassword'
import CreateCollectionLead from '../pages/collectionLead/CreateCollectionLead'
import RaisePickup from '../pages/raisePickup/RaisePickup'
import PriceComparison from '../pages/priceComparison/PriceComparison'
import Collections from '../pages/collections/Collections'
import ChooseLab from '../pages/chooseLab/ChooseLab'
import ViewCollectionDetails from '../pages/collections/pages/ViewCollectionDetails'
import ScheduledCollections from '../pages/scheduledCollections/ScheduledCollections'
import ViewScheduledCollectionDetail from '../pages/scheduledCollections/pages/ViewScheduledCollectionDetail'
import TestAcknowledge from '../pages/testAcknowledge/TestAcknowledge'
import ViewTestAcknowledgeDetails from '../pages/testAcknowledge/ViewTestAcknowledgeDetails'
import ReferPatient from '../pages/referPatient/ReferPatient'
import ReferPatientsList from '../pages/referPatient/ReferPatientsList'
import RaiseBloodRequirement from '../pages/bloodBank/RaiseBloodRequirement'
import PhleboBoyDetails from '../pages/phleboDetails/PhleboBoyDetails'
import OffersPage from '../pages/offers/OffersPage'
import Rewards from '../pages/rewards/Rewards'
import ReferApp from '../pages/referApp/ReferApp'
import FeedbackSuggestions from '../pages/feedback&suggetions/FeedbackSuggestions'
import Reports from '../pages/reports/Reports'
import PaymentHistory from '../pages/paymentHistory/PaymentHistory'
import PrivacyPolicy from '../pages/privacyPolicy/PrivacyPolicy'
import BillingSummary from '../pages/billingSummary/BillingSummary'
import Coupon from '../pages/chooseLab/Coupon'
import RaisePickupList from '../pages/raisePickup/RaisePickupList'
import PDFViewer from '../pages/pdfViewer/PDFViewer'

const StackNavigation = () => {
    const Stack = createNativeStackNavigator()
  return (
    <NavigationContainer>
        <Stack.Navigator screenOptions={{headerShown:false, animation:'slide_from_right',}}>
            {/* <Stack.Screen name='Login' component={Login}/> */}
            <Stack.Screen name='Login2' component={Login2}/>
            <Stack.Screen name='ForgotPassword' component={ForgotPassword}/>
            <Stack.Screen name='OtpVerification' component={OtpVerification}/>
            <Stack.Screen name='Main' component={BottomTabNavigation}/>
            <Stack.Screen name='CreateCollectionLead' component={CreateCollectionLead}/>
            <Stack.Screen name='RaisePickup' component={RaisePickup}/>
            <Stack.Screen name='RaisePickupList' component={RaisePickupList}/>
            <Stack.Screen name='OffersPage' component={OffersPage}/>
            <Stack.Screen name='Collections' component={Collections}/>
            <Stack.Screen name='ViewCollectionDetails' component={ViewCollectionDetails}/>
            <Stack.Screen name='ChooseLab' component={ChooseLab}/>
            <Stack.Screen name='Coupon' component={Coupon}/>
            <Stack.Screen name='ScheduledCollections' component={ScheduledCollections}/>
            <Stack.Screen name='ViewScheduledCollectionDetail' component={ViewScheduledCollectionDetail}/>
            <Stack.Screen name='TestAcknowledge' component={TestAcknowledge}/>
            <Stack.Screen name='ViewTestAcknowledgeDetails' component={ViewTestAcknowledgeDetails}/>
            <Stack.Screen name='ReferPatientsList' component={ReferPatientsList}/>
            <Stack.Screen name='ReferPatient' component={ReferPatient}/>
            <Stack.Screen name='RaiseBloodRequirement' component={RaiseBloodRequirement}/>
            <Stack.Screen name='PriceComparison' component={PriceComparison}/>
            <Stack.Screen name='Reports' component={Reports}/>
            <Stack.Screen name='PDFViewer' component={PDFViewer}/>
            <Stack.Screen name='ProfileManagement' component={ProfileManagement}/>
            <Stack.Screen name='ChangePassword' component={ChangePassword}/>
            <Stack.Screen name='PhleboBoyDetails' component={PhleboBoyDetails}/>
            <Stack.Screen name='BillingSummary' component={BillingSummary}/>
            <Stack.Screen name='PaymentHistory' component={PaymentHistory}/>
            <Stack.Screen name='Rewards' component={Rewards}/>
            <Stack.Screen name='ReferApp' component={ReferApp}/>
            <Stack.Screen name='PrivacyPolicy' component={PrivacyPolicy}/>
            <Stack.Screen name='FeedbackSuggestions' component={FeedbackSuggestions}/>
        </Stack.Navigator>
    </NavigationContainer>
  )
}

export default StackNavigation