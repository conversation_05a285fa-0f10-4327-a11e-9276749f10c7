import { View, Text, StyleSheet, Image, TouchableOpacity, ToastAndroid } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomButton from '../../components/CustomButton'
import CustomInput from '../../components/CustomInput'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import Colors from '../../styles/Colors'
import { TextInput } from 'react-native-paper';
import DeviceInfo from 'react-native-device-info'
import BottomLink from '../../components/BottomLink'
import CustomInput2 from '../../components/CustomInput2'
import BaseURL from '../../components/BaseURL'
import AsyncStorage from '@react-native-async-storage/async-storage'

const Login2 = ({navigation}) => {
  const currentYear = new Date().getFullYear();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [appVersion, setAppVersion] = useState('');

  const validation = () => {
     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!username || !password) {
      showToast('Please fill all fields');
      return false;
    }
     if (!emailRegex.test(username)) {
    showToast('Please enter a valid email address');
    return false;
    }
    if (password.length < 6) {
      showToast('Password must be at least 6 characters');
      return false;
    }
    return true;
  }

  const getToken = async () => {
        try {
            setIsLoading(true)
            const token = await AsyncStorage.getItem('authToken')
            if (token) {
                console.log(token)
                navigation.reset({ index: 0, routes: [{ name: 'Main' }] })
            }
        } catch (e) {
            console.log('catch', e)
        } finally {
            setIsLoading(false)
        }
  }

  useEffect(() => {
    const fetchVersion = async () => {
            try {
                const version = await DeviceInfo.getVersion();
                setAppVersion(version);
                console.log('App Version:', version);
            } catch (error) {
                console.log('Error fetching version:', error);
            }
    };
    fetchVersion();
    getToken();
  }, []);

  const handleLogin = async () => {
    if (!validation()) {
      return;
    }
    console.log('Login pressed with username:', username, 'and password:', password);

    try {
      setIsLoading(true);
      const baseUrl = await BaseURL.uri;
      const formData = new FormData();
      formData.append('email', username);
      formData.append('password', password);
      const response = await fetch(`${baseUrl}/collection-centers/login`, {
        method: 'POST',
        body: formData, 
      });
      const result = await response.json();
      console.log('Login response:', result);
      if (response.ok) {
          console.log(result)
          console.log(result.data.token)
          const token = result.data.token;
          if (token) {
            await AsyncStorage.setItem('authToken', token);
          }
          navigation.replace('Main',)
      } else {
        console.log('else', result)
        showToast(result.message || 'Something went wrong');
      }
    } catch (error) {
      console.error('Error during login:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleForgotPassword = () => {
    // Handle forgot password logic here
    console.log('Forgot Password pressed');
    // For example, you can navigate to a forgot password screen
    navigation.navigate('ForgotPassword');
  }

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.SHORT, ToastAndroid.CENTER);
  }

  return (
    <View style={styles.main}>
      <View style={styles.contentContainer}>
              <View style={styles.logoContainer}>
                  <Image
                    // source={{uri: 'https://verainterior.com/wp-content/uploads/2024/05/Dental-Clinic-Interior-Design-jpg.webp',}}
                    source={require('../../assets/Images/2.png')}
                    style={styles.logo}
                  />
                  <Text style={styles.appTitle}>SLD Hospital</Text>
                  <Text style={styles.appSubtitle}>Collection Center</Text>
              </View>
              <View style={styles.cardShadow}>
                <View style={styles.card}>
                    {/* <Text style={styles.welcomeText}>Welcome Back</Text>
                    <Text style={styles.loginText}>Login to your account</Text> */}

                    <CustomInput2 label="UserName" icon="account" keyboardType="email-address" value={username} onChangeText={setUsername} />
                    <CustomInput2 label="Password" icon="lock" value={password} onChangeText={setPassword} isPassword={true} />

                    <View style={{alignSelf: 'flex-start'}}>
                       <BottomLink subText={'Forgot Password?'} subTextColor={Colors.green} navigation={navigation} onPress={handleForgotPassword} />
                    </View>
                    <CustomButton title={'Login'} onPress={handleLogin} color={Colors.white} isLoading={isLoading}  />
                    {/* <View style={{alignSelf: 'center'}}>
                       <BottomLink text={'Don’t have an account? '} subText={'SignUp'} subTextColor={Colors.primary} navigation={navigation} onPress={()=> navigation.navigate('SignUp')} />
                    </View> */}
                </View>
              </View>
        </View>    
         <Text style={[styles.footerText,{color: Colors.tertiary,}]}>
           Version {appVersion}
        </Text>  
        <Text style={styles.footerText}>
            © {currentYear} Developed By IT Mingo. All rights reserved.
        </Text>  
    </View>
  )
}

export default Login2

const styles = StyleSheet.create({
  main: {
    flex:1,
    backgroundColor: Colors.white,
  },
  topCircle: {
    position: 'absolute',
    top: -responsiveWidth(25), // moves it partially above the screen
    left: responsiveWidth(10),
    width: responsiveWidth(50),
    height: responsiveWidth(50),
    borderRadius: responsiveWidth(25),
    backgroundColor: Colors.primary,
    // zIndex: -1, // behind other components
    },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(4),
    paddingTop: responsiveHeight(4),
    // paddingBottom: responsiveHeight(2),
    alignItems:'center',
    justifyContent:'center'
  },
  input: {
    backgroundColor: '#fff',
    fontSize: responsiveFontSize(1.8),
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(3),
    // backgroundColor:'red',
  },
  logo: {
    width: responsiveWidth(25),
    height: responsiveHeight(12),
    resizeMode: 'cover',
    borderRadius: responsiveWidth(3),
    borderWidth: 2,
    borderColor: Colors.primaryWithOpacity,
  },
  appTitle: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(1),
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  appSubtitle: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.green,
    marginTop: responsiveHeight(0.5),
    opacity: 0.9,
  },
  cardShadow: {
    width: '100%',
    borderRadius: responsiveWidth(4),
    // Shadow for iOS
    shadowColor: Colors.primaryWithExtraOpacity,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    // Elevation for Android
    elevation: 5,
    borderColor: Colors.primaryWithExtraOpacity,
    gap: responsiveHeight(2)
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(4),
    // padding: responsiveWidth(2),
    width: '100%',
    gap: responsiveHeight(2)
  },
  welcomeText: {
    fontSize: responsiveFontSize(2.5),
    fontWeight: 'bold',
    color: Colors.primary,
    textAlign: 'center',
  },
  loginText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.green ,
    // marginBottom: responsiveHeight(2),
    textAlign: 'center',
  },
  inputContainer: {
    gap: responsiveHeight(2),
  },
  forgotPass:{
    fontSize: responsiveFontSize(1.6),
    color: Colors.green ,
    textAlign: 'right',
  },
  footerText: {
    fontSize: responsiveFontSize(1.4),
    color: '#666',
    marginTop: responsiveHeight(3),
    textAlign: 'center',
    paddingBottom: responsiveHeight(1),
  },

  backgroundCircles: {
    position: 'absolute',
    width: '100%',
    height: responsiveHeight(30),
    top: 0,
    //   zIndex: -1,
 },

circleBig: {
  position: 'absolute',
  width: responsiveWidth(100),
  height: responsiveWidth(100),
  borderRadius: responsiveWidth(50),
  backgroundColor: `${Colors.primary}10`,
  top: -responsiveWidth(60),
  left: -responsiveWidth(30),
},

circleLeft: {
  position: 'absolute',
  width: responsiveWidth(30),
  height: responsiveWidth(30),
  borderRadius: responsiveWidth(15),
  backgroundColor: `${Colors.green}25`,
  top: -responsiveWidth(10),
  left: -responsiveWidth(15),
},

circleCenter: {
  position: 'absolute',
  width: responsiveWidth(60),
  height: responsiveWidth(60),
  borderRadius: responsiveWidth(30),
  backgroundColor: `${Colors.primary}20`,
  top: -responsiveWidth(30),
  alignSelf: 'center',
},

circleSmall: {
  position: 'absolute',
  width: responsiveWidth(30),
  height: responsiveWidth(30),
  borderRadius: responsiveWidth(15),
  backgroundColor: `${Colors.green}25`,
  top: -responsiveWidth(10),
  right: -responsiveWidth(15),
},

})