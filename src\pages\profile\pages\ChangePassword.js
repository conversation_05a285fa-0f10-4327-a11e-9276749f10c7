import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ToastAndroid,
} from 'react-native';
import { responsiveHeight, responsiveWidth, responsiveFontSize } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../../styles/Colors';
import CustomButton from '../../../components/CustomButton';
import MyHeader from '../../../components/MyHeader';
import CustomInput2 from '../../../components/CustomInput2';

const ChangePassword = ({ navigation }) => {
    const [form, setForm] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
    });

    const handleChange = (key, value) => {
        setForm(prev => ({ ...prev, [key]: value }));
    };

    const handleSubmit = () => {
        const { currentPassword, newPassword, confirmPassword } = form;

        if (!currentPassword || !newPassword || !confirmPassword) {
        showToast('Please fill in all fields.');
        return;
        }

        if (newPassword !== confirmPassword) {
        showToast('New password and confirm password do not match.');
        return;
        }

        // TODO: Send to API
        console.log('Changing password:', form);
    };

    const showToast = (msg)=> {
        ToastAndroid.showWithGravity(msg, ToastAndroid.TOP, ToastAndroid.SHORT);
    };

  return (
    <View style={styles.container}>
        <MyHeader title="Change Password" onBackPress={() => navigation.goBack()} />
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : null} style={{ flex: 1 }}>
            <ScrollView contentContainerStyle={styles.scroll}>
                <Text style={styles.label}>Current Password</Text>
                <CustomInput2
                    label="Current Password"
                    icon="lock"
                    value={form.currentPassword}
                    onChangeText={(text) => handleChange('currentPassword', text)}
                    // isPassword={true}
                />

                <Text style={styles.label}>New Password</Text>
                <CustomInput2
                    label="New Password"
                    icon="lock"
                    value={form.newPassword}
                    onChangeText={(text) => handleChange('newPassword', text)}
                    isPassword={true}
                />

                <Text style={styles.label}>Confirm Password</Text>
                <CustomInput2
                    label="Confirm Password"
                    icon="lock-check"
                    value={form.confirmPassword}
                    onChangeText={(text) => handleChange('confirmPassword', text)}
                    isPassword={true}
                />
                <View style={{marginTop: responsiveHeight(1)}}>
                    <CustomButton title="Change Password" onPress={handleSubmit} color={Colors.white} />
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    </View>
  )
}

export default ChangePassword


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scroll: {
    paddingHorizontal: responsiveWidth(5),
    paddingBottom: responsiveHeight(3),
  },
  label: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '500',
    marginTop: responsiveHeight(1.2),
    marginBottom: responsiveHeight(0.1),
    color: Colors.gray,
  },
});