import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ViewTestAcknowledgeDetails = ({ navigation }) => {
  const [isSharing, setIsSharing] = useState(false);
    const testDetail = {
        name: 'VITAMIN D3 TEST',
        aliases: [
        '1. 25-HYDROXYVITAMIN D',
        '2. 25(OH)D',
        '3. CALCIDIOL TEST',
        '5. 25-OH VIT',
        ],
        organ: 'Bones',
        sldCode: 'VSLD020',
        description:
        'Vitamin D is a nutrient required by the body to maintain healthy bones. It also supports the health of our immune system, muscle & brain. Vitamin D is not naturally found in foods unless fortified like fortified milk, cereal & salmon. Your body can only absorb calcium when vitamin D is present. Your body makes vitamin D when direct sunlight converts a chemical in your skin into active form of the vitamin. Risk factors for vitamin D deficiency are old age, lack of sun exposure, dark skin, malabsorption & obesity. Vitamin D deficiency increases the risk of osteoporosis, rickets, diabetes, heart disease & can lead to depression. This is the most widely used test that helps to diagnose whether you are deficient in vitamin D & require supplements.',
        specimenDetails: '3 mL (1.5 mL min.) Serum from 1 SST. Ship refrigerated or frozen.',
        preTestInfo: 'No special preparation required',
        testType: 'Bones',
        components: ['Vitamin Assays'],
        speciality: 'Orthopedician',
        usage:
        '25-Hydroxy vitamin D represents the main body reservoir and transport form. Mild to moderate deficiency is associated with Osteoporosis / Secondary Hyperparathyroidism while severe deficiency causes Rickets in children and Osteomalacia in adults. Prevalence of Vitamin D deficiency is approximately >50% especially in the elderly. This assay is useful for diagnosis of vitamin D deficiency and Hypervitaminosis D. It is also used for differential diagnosis of causes of Rickets & Osteomalacia and for monitoring Vitamin D replacement therapy.',
        reportTime: 'Same Day',
        containerType: 'SST (Gold)',
        stabilityRoom: '8 Hours',
        stabilityRefrigerator: '72 Hours',
        stabilityFrozen: '3 Weeks',
        fastingRequired: 'No',
        isPackage: 'No',
        isPopular: 'Yes',
        isSpecial: 'Yes',
        remark: '',
    };

    const handleShare = async () => {

    }


    const Badge = ({ label, value }) => {
        const isYes = value?.toLowerCase() === 'yes';
        return (
            <View
            style={[
                styles.badge,
                { backgroundColor: isYes ? Colors.success : Colors.gray },
            ]}
            >
            <Text style={styles.badgeText}>
                {label}: {value}
            </Text>
            </View>
        );
    };

    const renderAliases = (aliases) => (
        <View style={styles.row}>
        <Text style={styles.label}>Also Known As</Text>
        <View style={{ flex: 1 }}>
            {aliases.map((alias, index) => (
            <Text key={index} style={styles.aliasText}>• {alias}</Text>
            ))}
        </View>
        </View>
    );

    const renderRow = (label, value) => (
        <View style={styles.row}>
        <Text style={styles.label}>{label}</Text>
        <Text style={styles.value}>{value || '-'}</Text>
        </View>
    );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader 
        title="Test Details" 
        onBackPress={() => navigation.goBack()}
        rightComponent={
                  <TouchableOpacity onPress={handleShare} style={styles.shareButton} disabled={isSharing}>
                    {isSharing ? (
                      <ActivityIndicator size="small" color={Colors.primary} />
                    ) : (
                      <Icon name="share-variant" size={responsiveFontSize(3)} color={Colors.primary} />
                    )}
                  </TouchableOpacity>
        }
      />

      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>{testDetail.name}</Text>

        {renderAliases(testDetail.aliases)}
        {renderRow('Related Organ', testDetail.organ)}
        {renderRow('SLD Service Code', testDetail.sldCode)}

        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description}>{testDetail.description}</Text>

        <Text style={styles.sectionTitle}>Specimen Details</Text>
        {renderRow('Sample Info', testDetail.specimenDetails)}
        {renderRow('Pre-Test Info', testDetail.preTestInfo)}

        <Text style={styles.sectionTitle}>Test Type & Usage</Text>
        {renderRow('Test Type', testDetail.testType)}
        {renderRow('Component', testDetail.components.join(', '))}
        {renderRow('Speciality', testDetail.speciality)}

        <Text style={styles.sectionTitle}>Test Usage</Text>
        <Text style={styles.description}>{testDetail.usage}</Text>

        <Text style={styles.sectionTitle}>Reporting & Storage</Text>
        {renderRow('Report Time', testDetail.reportTime)}
        {renderRow('Container Type', testDetail.containerType)}
        {renderRow('Stability (Room)', testDetail.stabilityRoom)}
        {renderRow('Stability (Refrigerator)', testDetail.stabilityRefrigerator)}
        {renderRow('Stability (Frozen)', testDetail.stabilityFrozen)}
        <View style={styles.divider} />

        <Text style={styles.sectionTitle}>Additional Info</Text>
        <View style={styles.badgeContainer}>
          <Badge label="Fasting Required" value={testDetail.fastingRequired} />
          <Badge label="Is Popular" value={testDetail.isPopular} />
          <Badge label="Is Special" value={testDetail.isSpecial} />
          <Badge label="Is Package" value={testDetail.isPackage} />
        </View>
        {testDetail.remark ? renderRow('Remark', testDetail.remark) : null}
      </ScrollView>
    </View>
  )
}

export default ViewTestAcknowledgeDetails


const styles = StyleSheet.create({
  container: {
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(2),
  },
  title: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1.5),
  },
  sectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.black,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(0.5),
  },
  row: {
    flexDirection: 'row',
    marginBottom: responsiveHeight(0.8),
  },
  label: {
    fontWeight: 'bold',
    color: Colors.primary,
    fontSize: responsiveFontSize(1.6),
    width: responsiveWidth(42),
  },
   aliasText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
    marginBottom: 2,
  },
  value: {
    fontSize: responsiveFontSize(1.6),
    flex: 1,
    color: Colors.tertiary,
  },
  description: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
    textAlign: 'justify',
    marginBottom: responsiveHeight(1),
  },
  divider: {
    height: 1,
    backgroundColor: Colors.lightGray,
    marginVertical: responsiveHeight(1.2),
  },
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(2),
  },
  badge: {
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.8),
    borderRadius: responsiveWidth(6),
    backgroundColor: Colors.gray,
  },
  badgeText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.white,
    fontWeight: '600',
  },
  shareButton: {
    marginRight: responsiveWidth(3),
  },
});