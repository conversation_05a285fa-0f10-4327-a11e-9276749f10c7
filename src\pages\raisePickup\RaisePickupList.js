import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Searchbar } from 'react-native-paper';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

const RaisePickupList = ({ navigation }) => {
    const [searchQuery, setSearchQuery] = useState('');
    // const [expandedCardId, setExpandedCardId] = useState(null);
    const [expandedCardIds, setExpandedCardIds] = useState([]);
    const [pickupList, setPickupList] = useState([
        {
            id: '1',
            pickupDate: '2025-07-25',
            numVials: '3',
            remarks: 'Urgent',
            status: 'Pending',
            statusUpdates: [
                { step: 'Pickup Requested', time: '2025-07-25 09:00 AM' },
                { step: 'Accepted by <PERSON><PERSON><PERSON>', time: '2025-07-25 09:10 AM' },
                { step: 'Vials Collected', time: '2025-07-25 11:00 AM' },
            ],
        },
        {
            id: '2',
            pickupDate: '2025-07-24',
            numVials: '2',
            remarks: 'Routine',
            status: 'Completed',
            statusUpdates: [
                { step: 'Pickup Requested', time: '2025-07-24 08:30 AM' },
                { step: 'Accepted by Phlebo', time: '2025-07-24 08:40 AM' },
                { step: 'Vials Collected', time: '2025-07-24 10:00 AM' },
                { step: 'Sent to Lab', time: '2025-07-24 12:30 PM' },
                { step: 'Report Generated', time: '2025-07-25 02:00 PM' },
            ],
        },
    ]);

    const PICKUP_STATUS_STEPS = [ 'Pickup Requested', 'Accepted by Phlebo','Vials Collected', 'Sent to Lab', 'Report Generated' ];

    const filteredList = pickupList.filter(item =>
        item.remarks.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.pickupDate.includes(searchQuery)
    );

    const toggleExpand = (id) => {
        setExpandedCardIds((prev) =>
            prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
        );
    };



    const renderItem = ({ item }) => {
        // const isExpanded = expandedCardId === item.id;
        const isExpanded = expandedCardIds.includes(item.id);

        return (
            <View style={styles.card}>
                {/* Card Header */}
                <View style={styles.cardHeader}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Icon name="clipboard-list-outline" size={20} color={Colors.primary} />
                        <Text style={styles.pickupIdText}>Pickup ID: #{item.id}</Text>
                    </View>
                    <View style={[styles.statusBadge, { backgroundColor: item.status === 'Completed' ? `${Colors.green}20` : `${Colors.warning}40` }]}>
                        <Text style={styles.statusText}>{item.status}</Text>
                    </View>
                </View>

                {/* Pickup Info Rows */}
                <View style={{flexDirection: 'row', alignItems: 'center',justifyContent: 'space-between',}}>
                    <View style={styles.row}>
                        <Icon name="test-tube" size={responsiveFontSize(1.8)} color={Colors.tertiary} />
                        <Text style={styles.cardText}>Vials: <Text style={{color: Colors.black, fontWeight: '500', fontSize: responsiveFontSize(1.6)}}>{item.numVials}</Text></Text>
                    </View>
                    <View style={styles.row}>
                        <Icon name="calendar-clock" size={responsiveFontSize(1.8)} color={Colors.tertiary} />
                        <Text style={[styles.cardText, {color: Colors.black, fontSize: responsiveFontSize(1.6)}]}>{item.pickupDate}</Text>
                    </View>
                </View>
                <View style={styles.row}>
                    <Icon name="note-text-outline" size={responsiveFontSize(1.8)} color={Colors.tertiary} />
                    <Text style={styles.cardText}>Remarks: <Text style={{color: Colors.black, fontSize: responsiveFontSize(1.6)}}>{item.remarks}</Text></Text>
                </View>

                {/* Expand Section */}
                <View style={[styles.row, { justifyContent: 'space-between', marginBottom: responsiveHeight(0.1) }]}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        {/* <Icon name="information-outline" size={20} color={Colors.primary} />
                        <Text style={[styles.cardText, {fontWeight: '700', color: item.status === 'Completed' ? Colors.green : 'orange' }]}>
                            Status: {item.status}
                        </Text> */}
                    </View>
                    <TouchableOpacity style={styles.viewUpdates} onPress={() => toggleExpand(item.id)}>
                        <Text style={styles.viewUpdatesText}>{isExpanded ? 'Hide Updates' : 'View Updates'}</Text>
                        <Icon name={isExpanded ? 'chevron-up' : 'chevron-down'} size={responsiveFontSize(2.2)} color="#000" />
                    </TouchableOpacity>
                </View>

                {/* Timeline */}
                {isExpanded && (
                    <View style={styles.timelineContainer}>
                        {PICKUP_STATUS_STEPS.map((step, idx) => {
                        const foundUpdate = item.statusUpdates.find(s => s.step === step);
                        const currentStepIndex = item.statusUpdates.length - 1;
                        const isCompletedOrCurrent = idx <= currentStepIndex;

                        return (
                            <View key={idx} style={styles.timelineRow}>
                                <Icon
                                    name="check-circle"
                                    size={responsiveFontSize(2)}
                                    color={isCompletedOrCurrent ? Colors.green : Colors.gray}
                                    style={{padding: responsiveWidth(0.5), borderRadius: responsiveWidth(4), backgroundColor: isCompletedOrCurrent ? `${Colors.green}30` : `${Colors.gray}30`, }}
                                />
                                <View style={{ marginLeft: responsiveWidth(3) }}>
                                    <Text style={[styles.timelineStep, {color: isCompletedOrCurrent ? Colors.black : Colors.gray, fontWeight: isCompletedOrCurrent ? '600' : 'normal',}, ]}>
                                        {step}
                                    </Text>
                                    <Text style={styles.timelineTime}>
                                        {foundUpdate ? foundUpdate.time : 'Pending'}
                                    </Text>
                                </View>
                            </View>
                        );
                        })}
                    </View>
                )}

            </View>
        );
    };


  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <Icon name="truck-check-outline" size={responsiveFontSize(6)} color={Colors.primaryWithOpacity} />
        <Text style={styles.emptyTitle}>No Pickup Requests</Text>
        <Text style={styles.emptySubtitle}>You haven't raised any pickups yet. Start by submitting a new pickup request.</Text>
        {!searchQuery && (
          <TouchableOpacity
            style={styles.addAppointmentButton}
            onPress={() => navigation.navigate('RaisePickup')}
          >
            <Icon name="calendar-plus" size={responsiveFontSize(2)} color={Colors.white} />
            <Text style={styles.addAppointmentButtonText}>Raise New Pickup</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
        <MyHeader title="Pickup Requests" onBackPress={() => navigation.goBack()} onFabPress={() => navigation.navigate('RaisePickup')} fabTitle="Raise Pickup" />
        <View style={styles.searchBarContainer}>
            <Searchbar
                placeholder="Search here"
                onChangeText={setSearchQuery}
                value={searchQuery}
                style={styles.searchBar}
                inputStyle={styles.searchInput}
                iconColor={Colors.tertiary}
                placeholderTextColor={Colors.tertiary}
            />
        </View>
        <FlatList
            data={filteredList}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            renderItem={renderItem}
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
        />
    </View>
  );
};

export default RaisePickupList;

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: responsiveWidth(4),
    backgroundColor: Colors.white,
    paddingBottom: responsiveHeight(2),
  },
  card: {
    backgroundColor: '#F9FAFB',
    borderRadius: 10,
    padding: responsiveHeight(1.5),
    marginBottom: responsiveHeight(1.5),
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(0.8),
  },
  cardText: {
    marginLeft: responsiveWidth(2),
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: responsiveFontSize(2),
    color: Colors.gray,
    marginTop: responsiveHeight(5),
  },
  searchBarContainer: {
    padding: responsiveWidth(4),
    backgroundColor: Colors.white,
  },
  searchBar: {
      borderRadius: responsiveWidth(6),
      elevation: 2,
  },
  searchInput: {
      fontSize: responsiveFontSize(1.6),
      color: Colors.primary,
      fontWeight: '600',
      minHeight: responsiveHeight(4),
  },

  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: responsiveHeight(10),
  },
  emptyTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(2),
    marginBottom: responsiveHeight(0.5),
  },
  emptySubtitle: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
    textAlign: 'center',
    paddingHorizontal: responsiveWidth(8),
    marginBottom: responsiveHeight(3),
  },
  addAppointmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: responsiveWidth(6),
    paddingVertical: responsiveHeight(1.2),
    borderRadius: responsiveWidth(3),
    gap: responsiveWidth(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addAppointmentButtonText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.white,
    fontWeight: '600',
  },
  viewUpdates:{
      flexDirection: 'row',
      alignItems: 'center',
    //   backgroundColor: Colors.lightGray,
      paddingVertical: responsiveWidth(2),
      paddingLeft: responsiveWidth(2),
  },
  viewUpdatesText:{
      fontSize: responsiveFontSize(1.4),
      color: Colors.viewButton,
      fontWeight: '600'
  },
  timelineContainer: {
      paddingTop: responsiveHeight(1),
      marginTop: responsiveHeight(1),
      paddingLeft: responsiveWidth(2),
      borderLeftWidth: 2,
      borderLeftColor: Colors.primary,
  },
  timelineRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: responsiveHeight(0.8),
  },
  timelineStep: {
      fontSize: responsiveFontSize(1.6),
      fontWeight: '400',
      color: Colors.black,
  },
  timelineTime: {
      fontSize: responsiveFontSize(1.3),
      color: Colors.tertiary,
  },
  cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: responsiveHeight(1),
      borderBottomWidth: 1,
      borderColor: Colors.primaryWithOpacity,
      paddingBottom: responsiveHeight(1),
  },
  pickupIdText: {
      marginLeft: responsiveWidth(2),
      fontSize: responsiveFontSize(1.8),
      fontWeight: '700',
      color: Colors.primary,
  },
  statusBadge:{
      paddingHorizontal: responsiveWidth(2),
      paddingVertical: responsiveHeight(0.4),
      borderRadius: responsiveWidth(6),
  },
  statusText: {
      fontSize: responsiveFontSize(1.4),
      color: Colors.primary,
      fontWeight: '700',
  },


});
