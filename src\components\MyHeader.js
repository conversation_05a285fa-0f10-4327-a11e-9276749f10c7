import * as React from 'react';
import { StyleSheet, TouchableOpacity, Text  } from 'react-native';
import { Appbar } from 'react-native-paper';
import Colors from '../styles/Colors';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';


const MyHeader = ({ title, onBackPress, onFabPress, fabTitle, rightComponent  }) => {
  return (
    <Appbar.Header style={styles.header}>
        {onBackPress && <Appbar.BackAction onPress={onBackPress} color={Colors.primary} size={responsiveFontSize(2.5)} />}
        <Appbar.Content title={title} titleStyle={[styles.title,{paddingLeft: onBackPress ? responsiveWidth(0) : responsiveWidth(1)}]} />
        {onFabPress && (
          <TouchableOpacity onPress={onFabPress} style={styles.fabButton}>
            <Icon name="plus" size={responsiveFontSize(2.4)} color={Colors.white} />
            <Text style={styles.fabText}>{fabTitle}</Text>
          </TouchableOpacity>
        )}
        {rightComponent && rightComponent}
    </Appbar.Header>
  )
}

export default MyHeader

const styles = StyleSheet.create({
  header: {
    backgroundColor: Colors.white,
    // height: responsiveHeight(10), // Reduced height
    elevation: 0,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: responsiveFontSize(2),
    color: Colors.primary,
    fontWeight: 'bold',
    
  },
  fabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: responsiveWidth(2),
    paddingVertical: responsiveHeight(0.6),
    borderRadius: responsiveWidth(12),
    marginRight: responsiveWidth(3.5),
    gap: responsiveWidth(1),
  },
  fabText:{
    fontSize: responsiveFontSize(1.4),
    color: Colors.white,
    fontWeight: 'bold'
  },
});