import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Searchbar } from 'react-native-paper';

// Sample data (you can fetch this from API)
const referredPatients = [
  {
    id: '1',
    hospital: 'Hospital A',
    name: '<PERSON>',
    age: 32,
    sex: 'Male',
    phone: '**********',
    problemType: 'OPD',
    specialty: 'Cardiology',
  },
  {
    id: '2',
    hospital: 'Hospital B',
    name: '<PERSON><PERSON>',
    age: 27,
    sex: 'Female',
    phone: '**********',
    problemType: 'IP',
    specialty: 'Orthopedics',
  },
];

const ReferPatientsList = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredReferredPatients = referredPatients.filter(
        (item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.hospital.toLowerCase().includes(searchQuery.toLowerCase())
  );

    const renderItem = ({ item }) => (
    <View style={styles.card}>
      <View style={styles.row}>
        <Icon name="hospital-building" size={responsiveFontSize(1.6)} color={Colors.primary} />
        <Text style={styles.label}>{item.hospital}</Text>
      </View>

      <View style={styles.row}>
        <Icon name="account" size={responsiveFontSize(1.6)} color={Colors.primary} />
        <Text style={styles.label}>{item.name} ({item.age} yrs, {item.sex})</Text>
      </View>

      <View style={styles.row}>
        <Icon name="phone" size={responsiveFontSize(1.6)} color={Colors.primary} />
        <Text style={styles.label}>{item.phone}</Text>
      </View>

      <View style={styles.row}>
        <Icon name="alert-circle-outline" size={responsiveFontSize(1.6)} color={Colors.primary} />
        <Text style={styles.label}>Problem: {item.problemType}</Text>
      </View>

      <View style={styles.row}>
        <Icon name="stethoscope" size={responsiveFontSize(1.6)} color={Colors.primary} />
        <Text style={styles.label}>Specialty: {item.specialty}</Text>
      </View>

      {/* <TouchableOpacity
        style={styles.viewButton}
        onPress={() => console.log('Navigate to patient detail')}
      >
        <Text style={styles.viewButtonText}>View Details</Text>
      </TouchableOpacity> */}
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Referred Patients" onBackPress={() => navigation.goBack()} fabTitle={'Refer Patient'} onFabPress={() => navigation.navigate('ReferPatient')} />
      <View style={styles.container}>
        <Searchbar
          placeholder="Search by test name or SLD code"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.tertiary}
          placeholderTextColor={Colors.tertiary}
        />

        <FlatList
          data={filteredReferredPatients}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          renderItem={renderItem}
          ListEmptyComponent={<Text style={styles.emptyText}>No referrals found.</Text>}
        />

      </View>

    </View>
  )
}

export default ReferPatientsList


const styles = StyleSheet.create({
   container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
  },
   searchBar: {
    marginBottom: responsiveHeight(1.5),
    borderRadius: responsiveWidth(6),
    elevation: 2,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    fontWeight: '600',
    minHeight: responsiveHeight(4),
  },
  listContainer: {
    // padding: responsiveWidth(4),
    paddingBottom: responsiveHeight(4),
  },
  card: {
    backgroundColor: Colors.white,
    padding: responsiveWidth(4),
    borderRadius: responsiveWidth(3),
    marginBottom: responsiveHeight(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(0.8),
  },
  label: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.tertiary,
    marginLeft: responsiveWidth(2),
  },
  viewButton: {
    alignSelf: 'flex-start',
    backgroundColor: Colors.primary,
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(1),
    borderRadius: responsiveWidth(5),
    marginTop: responsiveHeight(1.5),
  },
  viewButtonText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.white,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: responsiveFontSize(2),
    color: Colors.tertiary,
    textAlign: 'center',
    marginTop: responsiveHeight(10),
  },
});