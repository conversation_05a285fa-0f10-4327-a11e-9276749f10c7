import { View, Text, StyleSheet, TouchableOpacity, Switch } from 'react-native'
import React from 'react'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../styles/Colors';

const SettingComponent = ({icon, title, subtitle, toggle, value, onPress, showToggle = true, color = Colors.black}) => {
  return (
    <TouchableOpacity onPress={onPress} style={styles.settingsItem} disabled={!onPress} >
      <View style={styles.settingsItemLeft}>
        <View style={[styles.settingsIconContainer, { backgroundColor: `${color}20` }]}>
          <Icon name={icon} size={responsiveFontSize(2.5)} color={color} />
        </View>
        <View style={styles.settingsTextContainer}>
          <Text style={styles.settingsTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingsSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showToggle && (
        <Switch
          trackColor={{ false: Colors.lightGray, true: Colors.primaryWithOpacity }}
          thumbColor={value ? Colors.primary : Colors.gray}
          ios_backgroundColor={Colors.lightGray}
          onValueChange={toggle}
          value={value}
        />
      )}
      {!showToggle && (
        <Icon name="chevron-right" size={responsiveFontSize(2.5)} color={Colors.tertiary} />
      )}
    </TouchableOpacity>
  )
}

export default SettingComponent

const styles = StyleSheet.create({
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: responsiveHeight(1),
    borderBottomWidth: 1,
    borderBottomColor: Colors.primaryWithExtraOpacity,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsIconContainer: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(5),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: responsiveWidth(3),
  },
  settingsTextContainer: {
    flex: 1,
  },
  settingsTitle: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.black,
  },
  settingsSubtitle: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.tertiary,
    marginTop: responsiveHeight(0.2),
  },
});
