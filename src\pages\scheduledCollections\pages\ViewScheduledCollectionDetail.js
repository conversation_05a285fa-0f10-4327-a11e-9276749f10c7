import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import MyHeader from '../../../components/MyHeader';
import Colors from '../../../styles/Colors';

  const dummy = {
      id: '1',
      bookingNo: 'BK-240712-01',
      name: '<PERSON>',
      age: '35',
      gender: 'Male',
      phone: '9876543210',
      address: '123 Street, City A',
      testNames: [
        { name: 'CBC TEST', SLDCode: 'SLDC126', price: 500, statusUpdates: [{ step: 'Order Confirmed', time: '2025-07-02 09:00 AM' },{ step: 'Sent to Lab', time: '2025-07-02 09:59 AM' },{ step: 'Report Generated', time: '2025-07-02 09:59 PM' },], },
        { name: 'Urine Test', SLDCode: 'USLD014', price: 550,  statusUpdates: [{ step: 'Order Confirmed', time: '2025-07-02 09:00 AM' },], },
      ],
      collectionType: 'Draw',
      labName: 'Dr. Path Lab',
      labImage: 'https://picsum.photos/seed/lab1/100/100',
      date: '02-07-2025',
      paymentDate: '01-07-2025',
      amount: 1050,
      collectionCharge: 100,
      paymentMethod: 'UPI',
      paymentStatus: 'Paid',
      status: 'Confirmed',
  };

  const STATUS_STEPS = ['Order Confirmed', 'Sent to Lab', 'Report Generated'];


const ViewScheduledCollectionDetail = ({ navigation }) => {
  // mock data (replace this with state or Redux later)
  const [item, setItem] = useState(null);
  // const [expandedIndex, setExpandedIndex] = useState(null);
  const [expandedIndices, setExpandedIndices] = useState([]);

  useEffect(() => {
    setItem(dummy);
  }, []);

  if (!item) return null;

  const grandTotal = item.amount + item.collectionCharge;


  const toggleExpand = (index) => {
    if (expandedIndices.includes(index)) {
      setExpandedIndices(expandedIndices.filter(i => i !== index)); // collapse this
    } else {
      setExpandedIndices([...expandedIndices, index]); // expand this
    }
  };

  const SectionCard = ({ title, children }) => (
    <View style={styles.card}>
      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: responsiveHeight(1), }}>
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
      {children}
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Booking Details" onBackPress={() => navigation.goBack()} />
      <ScrollView contentContainerStyle={styles.container}>

        {/* Patient Info */}
        <SectionCard title="Current Status">
          <View style={[styles.currentStatus]}>
            <View style={styles.testStatusContainer}>
              <Text style={styles.testStatus}>{item.status}</Text>
            </View>
            <Text style={styles.subLabel}>{item.date}</Text>
          </View>
        </SectionCard>

        {/* Patient Info */}
        <SectionCard title="Sample Collection Details">
          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: responsiveHeight(0.5) }}>
            <Text style={[styles.label,{color: Colors.tertiary}]}>Name: <Text style={{color: Colors.primary}}>{item.name}</Text></Text>
            <Text style={[styles.label,{color: Colors.tertiary}]}>Collection Type: <Text style={{color: Colors.primary}}>{item.collectionType}</Text></Text>
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: responsiveHeight(0.5) }}>
            <Text style={styles.subLabel}>Age: {item.age} | {item.gender}</Text>
            <Text style={styles.subLabel}>Phone: {item.phone}</Text>
          </View>
          <Text style={styles.subLabel}>Address: {item.address}</Text>
        </SectionCard>

        {/* Test Details */}
        <SectionCard title="Test Details">
          {item.testNames.map((test, index) => (
            <View key={index} style={styles.testCard}>
              <View style={[styles.testDetail,{ flex: 1 }]}>
                <Text style={styles.testName}>{test.SLDCode}</Text>
                <Text style={styles.testPrice}>₹{test.price}</Text>
              </View>

              <Text style={styles.testCode}>
                Test Name: <Text style={{color: Colors.black, fontSize: responsiveFontSize(1.5), fontWeight: '600'}}>{test.name}</Text>
              </Text>

              <View style={styles.testStatusRow}>
                <View style={[styles.testStatusContainer]}>
                  <Text style={styles.testStatus}>{item.status}</Text>
                </View>
                {/* <TouchableOpacity onPress={() => setExpandedIndex(index === expandedIndex ? null : index)} style={styles.viewUpdates}>
                  <Text style={styles.viewUpdatesText}>{index === expandedIndex ? 'Hide Updates' : 'View Updates'}</Text>
                  <Icon name={index === expandedIndex ? 'chevron-up' : 'chevron-down'} size={responsiveFontSize(2.2)} color="#000" />
                </TouchableOpacity> */}
                <TouchableOpacity onPress={() => toggleExpand(index)} style={styles.viewUpdates}>
                  <Text style={styles.viewUpdatesText}>{expandedIndices.includes(index) ? 'Hide Updates' : 'View Updates'}</Text>
                  <Icon  name={expandedIndices.includes(index) ? 'chevron-up' : 'chevron-down'} size={responsiveFontSize(2.2)} color="#000" />
                </TouchableOpacity>
              </View>

              {/* Collapsible Status Updates */}
              {/* {index === expandedIndex && ( */}
              {expandedIndices.includes(index) && (
                <View style={styles.statusContainer}>
                  {STATUS_STEPS.map((status, idx) => {
                    const foundUpdate = test.statusUpdates.find(s => s.step === status);
                    const currentStepIndex = test.statusUpdates.length - 1;

                    const isCompletedOrCurrent = idx <= currentStepIndex;
                    
                    return (
                      <View key={idx} style={styles.statusRow}>
                        <Icon
                          name={'check-circle'}
                          size={responsiveFontSize(2)}
                          color={isCompletedOrCurrent ? Colors.green : Colors.gray}
                          style={{padding: responsiveWidth(0.5), borderRadius: responsiveWidth(4), backgroundColor: isCompletedOrCurrent ? `${Colors.green}30` : `${Colors.gray}30`}}
                        />
                        <View style={{ marginLeft: responsiveWidth(3) }}>
                          <Text style={[styles.statusText, {color: isCompletedOrCurrent ? Colors.black : Colors.gray, fontWeight: isCompletedOrCurrent  ? '600' : 'normal',}]}>
                            {status}
                          </Text>
                          <Text style={styles.timelineTime}>
                            {foundUpdate ? foundUpdate.time : 'Pending'}
                          </Text>
                        </View>
                      </View>
                    );
                  })}
                </View>
              )}

            </View>
          ))}
        </SectionCard>

        {/* Lab Info */}
        <SectionCard title="Lab Details">
          <View style={styles.labInfo}>
            <Image source={{ uri: item.labImage }} style={styles.labImage} />
            <View>
              <Text style={styles.label}>{item.labName}</Text>
            </View>
          </View>
        </SectionCard>

        {/* Price Info */}
        <SectionCard title="Price Details">
          <View style={{paddingHorizontal: responsiveWidth(2)}}>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Subtotal:</Text>
              <Text style={styles.rightTextRow}>₹{item.amount}</Text>
            </View>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Collection Charge:</Text>
              <Text style={styles.rightTextRow}>₹{item.collectionCharge}</Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalText}>Grand Total:</Text>
              <Text style={styles.totalAmount}>₹{grandTotal}</Text>
            </View>
          </View>
        </SectionCard>

        {/* Other Info */}
        <SectionCard title="Other Details">
           <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Booking No:</Text>
              <Text style={styles.rightTextRow}>{item.bookingNo}</Text>
            </View>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Booking Date:</Text>
              <Text style={styles.rightTextRow}>{item.date}</Text>
            </View>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Payment Date:</Text>
              <Text style={styles.rightTextRow}>{item.paymentDate}</Text>
            </View>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Payment Mode:</Text>
              <Text style={styles.rightTextRow}>{item.paymentMethod}</Text>
            </View>
            <View style={styles.chargeRow}>
              <Text style={styles.leftTextRow}>Payment Status:</Text>
              <Text style={[styles.rightTextRow, { color: item.paymentStatus === 'Paid' ? Colors.green : Colors.error, fontSize: responsiveFontSize(1.8) }]}>{item.paymentStatus}</Text>
            </View>
          {/* <Text style={styles.subLabel}>Booking No: {item.bookingNo}</Text> */}
          {/* <Text style={styles.subLabel}>Booking Date: {item.date}</Text> */}
          {/* <Text style={styles.subLabel}>Payment Date: {item.paymentDate}</Text> */}
          {/* <Text style={styles.subLabel}>Payment Mode: {item.paymentMethod}</Text> */}
          {/* <Text style={[styles.subLabel, { color: item.paymentStatus === 'Paid' ? Colors.green : Colors.red }]}>
            Payment Status: {item.paymentStatus}
          </Text> */}
        </SectionCard>

      </ScrollView>
    </View>
  );
};

export default ViewScheduledCollectionDetail;

const styles = StyleSheet.create({
  container: {
    padding: responsiveWidth(4),
    paddingBottom: responsiveHeight(5),
  },
  card: {
    backgroundColor: Colors.background,
    borderRadius: responsiveWidth(2),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
    elevation: 2,
  },
  cardTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.black,
    // marginBottom: responsiveHeight(1),
  },
  label: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: '600',
    color: Colors.black,
    // marginBottom: 4,
  },
  subLabel: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
    marginBottom: 2,
  },
testCard: {
  // backgroundColor: Colors.background,
  paddingHorizontal: responsiveWidth(3),
  paddingVertical: responsiveHeight(1),
  borderRadius: 10,
  marginBottom: responsiveHeight(1),
  gap: responsiveWidth(1),
  borderWidth: 0.6,
  borderColor: Colors.gray
},
testDetail:{
 flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginTop: responsiveHeight(0.5),
},
testStatusContainer:{
  backgroundColor: Colors.success,
  borderRadius: responsiveWidth(4),
  paddingHorizontal: responsiveWidth(2.2),
  paddingVertical: responsiveHeight(0.5),
},
testStatus:{
  color: Colors.white,
  fontSize: responsiveFontSize(1.6),
  textTransform: 'capitalize',
  fontWeight: 'bold',
},
testUpdate:{
  color: Colors.viewButton
},
testName: {
  fontSize: responsiveFontSize(1.7),
  color: Colors.primary,
  fontWeight: 'bold',
},
testCode: {
  fontSize: responsiveFontSize(1.3),
  color: Colors.tertiary,
},
testPrice: {
  fontSize: responsiveFontSize(1.7),
  color: Colors.black,
  fontWeight: '600',
},

  labInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(2),
  },
  labImage: {
    width: responsiveWidth(7),
    height: responsiveWidth(7),
    borderRadius: responsiveWidth(5),
    marginRight: responsiveWidth(3),
  },
  leftTextRow:{
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
  },
  rightTextRow:{ 
    fontSize: responsiveFontSize(1.7),
    color: Colors.black,
    fontWeight: '600',
  },
  priceRow: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.black,
  },
  chargeRow:{
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: responsiveHeight(0.5)
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: responsiveHeight(1),
    borderTopWidth: 1,
    borderColor: '#ccc',
    paddingTop: responsiveHeight(0.8),
  },
  totalText: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
    color: Colors.black,
  },
  totalAmount: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '600',
    color: Colors.green,
  },
  statusContainer: {
  marginTop: responsiveHeight(1),
  paddingLeft: responsiveWidth(2),
  borderLeftWidth: 2,
  borderLeftColor: Colors.primary,
},
statusRow: {
  flexDirection: 'row',
  alignItems: 'flex-start',
  marginBottom: responsiveHeight(0.8),
},
statusText: {
  fontSize: responsiveFontSize(1.6),
  color: Colors.gray,
  fontWeight: '500',
},
 timelineTime: {
      fontSize: responsiveFontSize(1.3),
      color: Colors.tertiary,
  },
currentStatus:{
   backgroundColor: `${Colors.green}10`,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      paddingVertical: responsiveHeight(1),
      paddingHorizontal: responsiveWidth(4),
      borderRadius: responsiveWidth(5),
      borderWidth: 1,
      borderColor: Colors.green,
},
  testStatusRow:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: responsiveHeight(0.5),
  },
  viewUpdates:{
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewUpdatesText:{
    fontSize: responsiveFontSize(1.4),
    color: Colors.viewButton,
    fontWeight: '600'
  },
});
