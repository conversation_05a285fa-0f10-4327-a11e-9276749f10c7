const hexToRgba = (hex, opacity) => {
    const bigint = parseInt(hex.replace('#', ''), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

const Colors = {
    // Primary colors
    primary: '#006599',       // Deep blue
    secondary: '#FF6B35',     // Orange accent - for buttons and highlights
    tertiary: '#797979',
    
    // Background colors
    background: '#F5F7FA',    // Light gray background
    surfaceColor: '#FFFFFF',  // White surface color
    
    // Text colors
    textPrimary: '#333333',   // Dark gray for primary text
    textSecondary: '#666666', // Medium gray for secondary text
    
    // Status colors
    success: '#28A745',       // Green for success messages
    error: '#DC3545',         // Red for error messages
    warning: '#FFC107',       // Yellow for warnings
    info: '#17A2B8',          // Blue for information

    // Action button colors
    viewButton: '#4dabf7',    // Blue
    addButton: '#20c997',     // Green
    editButton: '#fcc419',    // Yellow
    deleteButton: '#fa5252',  // Red
    
    // Standard colors
    white: '#FFFFFF',
    black: '#000000',
    gray: '#AAAAAA',
    lightGray: '#EEEEEE',
    darkGray: '#555',
    pink: '#FF69B4',
    lightPink: '#FFB6C1',
    darkPink: '#C2185B',
    
    // Government specific
    govBlue: '#004D90',       // Official blue
    govOrange: '#FF6B35',     // Complementary orange
    green: '#40a403',      // Green for environmental themes
    govBrown: '#6D4C3D',      // Brown for earth/waste management

     // Card background colors
    card1:'#CEE8F1',
    card2:'#FBECDD',
    card3:'#D1F9E7',
    card4:'#FFE7F7',
    card5: '#F4E6FF',
    card6: '#E6E6FA',
    card7: '#DDFFFC',
    card8: '#8fe4f1',

    high: '#D32F2F',
    medium: '#FFA000',
    low: '#388E3C',

    greenLight: '#effbe8ff',
}

Colors.primaryWithOpacity= hexToRgba(Colors.primary, 0.3)
Colors.primaryWithExtraOpacity= hexToRgba(Colors.primary, 0.1)
Colors.primaryDropDownOpacity= hexToRgba(Colors.primary, 0.8)

export default Colors;