import { View, Text, StyleSheet, Animated, Image } from 'react-native';
import React, { useState } from 'react';
import {responsiveFontSize, responsiveHeight, responsiveWidth,} from 'react-native-responsive-dimensions';
import {<PERSON><PERSON>ield, Cursor, useBlurOnFulfill, useClearByFocusCell, } from 'react-native-confirmation-code-field';
import Colors from '../styles/Colors';


const { Value, Text: AnimatedText } = Animated;
const source = {
    uri: 'https://user-images.githubusercontent.com/4661784/56352614-4631a680-61d8-11e9-880d-86ecb053413d.png',
};

const animationsColor = [...new Array(6)].map(() => new Value(0));
const animationsScale = [...new Array(6)].map(() => new Value(1));

const animateCell = ({ hasValue, index, isFocused }) => {
    Animated.parallel([
        Animated.timing(animationsColor[index], {
            useNativeDriver: false,
            toValue: isFocused ? 1 : 0,
            duration: 100,
        }),
        Animated.spring(animationsScale[index], {
            useNativeDriver: false,
            toValue: hasValue ? 0 : 1,
            duration: hasValue ? 150 : 100,
        }),
    ]).start();
};


const OtpBox = ({number, title}) => {
    const [value, setValue] = useState('');
    const ref = useBlurOnFulfill({ value, cellCount: 6 });
    const [props, getCellOnLayoutHandler] = useClearByFocusCell({
        value,
        setValue,
    });

    const renderCell = ({ index, symbol, isFocused }) => {
        const hasValue = Boolean(symbol);
        const animatedCellStyle = {
            backgroundColor: hasValue
                ? animationsScale[index].interpolate({
                    inputRange: [0, 1],
                    outputRange: [Colors.surfaceColor, Colors.primary],
                })
                : animationsColor[index].interpolate({
                    inputRange: [0, 1],
                    outputRange: [Colors.background, Colors.surfaceColor],
                }),
            borderRadius: animationsScale[index].interpolate({
                inputRange: [0, 1],
                outputRange: [responsiveFontSize(1), responsiveFontSize(1)],
            }),
            transform: [
                {
                    scale: animationsScale[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.8, 1],
                    }),
                },
            ],
        };

        setTimeout(() => {
            animateCell({ hasValue, index, isFocused });
        }, 0);

        return (
            <AnimatedText
                key={index}
                style={[styles.cell, animatedCellStyle,{ fontSize: symbol ? responsiveFontSize(3) : responsiveFontSize(2.5) }]}
                onLayout={getCellOnLayoutHandler(index)}
            >
                {symbol || (isFocused ? <Cursor /> : null)}
            </AnimatedText>
        );
    };


  return (
    <View style={styles.root}>
            <Text style={styles.title}>{title}</Text>
            <Image style={styles.icon} source={source} />
            <Text style={styles.subTitle}>
                Please enter the verification code{'\n'}we send to your {number}
            </Text>

            <CodeField
                ref={ref}
                {...props}
                value={value}
                onChangeText={setValue}
                cellCount={6}
                rootStyle={styles.codeFieldRoot}
                keyboardType="number-pad"
                textContentType="oneTimeCode"
                renderCell={renderCell}
            />
    </View>
  )
}

export default OtpBox



const styles = StyleSheet.create({
    root: {
        // flex:1,
        // paddingHorizontal: responsiveWidth(6),
        // justifyContent:'center'
    },
    title: {
        paddingTop: responsiveHeight(6),
        paddingBottom: responsiveHeight(5),
        color: '#000',
        fontSize: responsiveFontSize(3),
        fontWeight: '700',
        textAlign: 'center',
    },
    icon: {
        width: responsiveWidth(30),
        height: responsiveHeight(15),
        alignSelf: 'center',
    },
    subTitle: {
        paddingTop: responsiveHeight(4),
        color: '#000',
        fontSize: responsiveFontSize(1.8),
        textAlign: 'center',
    },
    codeFieldRoot: {
        height: responsiveHeight(9),
        marginTop: responsiveHeight(3),
        paddingHorizontal: responsiveWidth(5),
        justifyContent: 'center',
        // alignItems:'center'
    },
    cell: {
        width: responsiveWidth(11),
        height: responsiveWidth(11),
        lineHeight: responsiveHeight(5),
        fontSize: responsiveFontSize(3),
        borderWidth: responsiveWidth(0.3),
        borderColor: Colors.primary,
        textAlign: 'center',
        marginHorizontal: responsiveWidth(1),
        color: Colors.primary,
    },
});