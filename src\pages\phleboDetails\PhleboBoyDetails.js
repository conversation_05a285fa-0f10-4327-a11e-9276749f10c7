import React from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

const PhleboBoyDetails = ({navigation}) => {
    const phlebo = {
        name: '<PERSON><PERSON>',
        phone: '+91 9876543210',
        gender: 'Male',
        email: '<EMAIL>',
        area: 'South Delhi',
        id: 'PH1234',
        shift: '9 AM – 5 PM',
        availability: 'Available',
        status: 'Active',
    };

    const renderDetailRow = (icon, label, value, valueColor = Colors.black) => (
        <View style={styles.row}>
            <MaterialCommunityIcons name={icon} size={responsiveFontSize(2.4)} color={Colors.primary} style={styles.icon} />
            <View style={{ flex: 1 }}>
                <Text style={styles.label}>{label}</Text>
                <Text style={[styles.value, { color: valueColor }]}>{value}</Text>
            </View>
        </View>
    );

  return (
    <View style={{flex:1, backgroundColor: Colors.white,}}>
        <MyHeader title={'Phlebo Boy Details'} onBackPress={() => navigation.goBack()} />
        <ScrollView contentContainerStyle={styles.container}>
            {/* Profile Image */}
            <View style={styles.profileContainer}>
                <Image
                    source={{ uri: 'https://randomuser.me/api/portraits/men/75.jpg' }}
                    style={styles.profileImage}
                    resizeMode='stretch'
                />
                <Text style={styles.nameText}>{phlebo.name}</Text>
            </View>

            {/* Details Card */}
            <View style={styles.card}>
                {renderDetailRow('phone', 'Phone Number', phlebo.phone)}
                {renderDetailRow('email', 'Email', phlebo.email)}
                {renderDetailRow('gender-male-female', 'Gender', phlebo.gender)}
                {renderDetailRow('map-marker', 'Assigned Area', phlebo.area)}
                {renderDetailRow('card-account-details', 'ID Number', phlebo.id)}
                {renderDetailRow('clock-outline', 'Shift Timing', phlebo.shift)}
                {renderDetailRow('check-circle', 'Availability', phlebo.availability, Colors.success)}
                {renderDetailRow('account-check', 'Status', phlebo.status, Colors.primary)}
            </View>
        </ScrollView>
    </View>
  )
}

export default PhleboBoyDetails

const styles = StyleSheet.create({
  container: {
    padding: responsiveWidth(4),
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  profileImage: {
    width: responsiveWidth(24),
    height: responsiveWidth(24),
    borderRadius: responsiveWidth(12),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  nameText: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    marginTop: responsiveHeight(1),
    color: Colors.primary,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(4),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: responsiveHeight(2),
  },
  icon: {
    marginRight: responsiveWidth(3),
    marginTop: responsiveHeight(0.5),
  },
  label: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
    fontWeight: '500',
  },
  value: {
    fontSize: responsiveFontSize(2),
    fontWeight: '600',
  },
});