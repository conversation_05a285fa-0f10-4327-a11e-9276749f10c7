import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Linking,
  Platform,
  ToastAndroid,
} from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Searchbar } from 'react-native-paper';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';
import Clipboard from '@react-native-clipboard/clipboard';


const bloodBankData = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    bloodGroup: 'A+',
    unitsRequired: '2',
    hospital: 'City Hospital',
    urgency: 'High',
    phone: '**********',
  },
  {
    id: '2',
    name: '<PERSON>riya Mehra',
    bloodGroup: 'B-',
    unitsRequired: '1',
    hospital: 'Global Health Care',
    urgency: 'Low',
    phone: '**********',
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON>',
    bloodGroup: 'AB-',
    unitsRequired: '1',
    hospital: 'Health Care',
    urgency: 'Medium',
    phone: '**********',
  },
  {
    id: '4',
    name: 'Anaya Mishra',
    bloodGroup: 'O+',
    unitsRequired: '1',
    hospital: 'Global Health Care',
    urgency: 'Low',
    phone: '**********',
  },
];




const BloodBank = ({ navigation }) => {
    const [searchQuery, setSearchQuery] = useState('');

    const filteredData = bloodBankData.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const getBloodGroupColor = (bg) => {
        if (bg.includes('+')) return '#1976D2'; // Blue
        if (bg.includes('-')) return '#D32F2F'; // Red
        return '#455A64'; // Neutral
    };

    const getUrgencyColor = (urgency) => {
        switch (urgency) {
            case 'High':
            return '#D32F2F';
            case 'Medium':
            return '#FFA000';
            case 'Low':
            default:
            return '#388E3C';
        }
    };



   const renderItem = ({ item }) => (
  <View style={styles.cardNew}>
    <View style={styles.cardHeader}>
      <Text style={styles.patientName}>{item.name}</Text>

      <View style={[styles.badge, { backgroundColor: Colors.primary, }]}>
        <Text style={styles.badgeText}>{item.bloodGroup}</Text>
      </View>
    </View>

    <View style={styles.detailRow}>
      <Icon name="flask" size={responsiveFontSize(2)} color={Colors.tertiary} />
      <Text style={styles.detailText}>Units Required: {item.unitsRequired}</Text>
    </View>

    <View style={styles.detailRow}>
      <Icon name="hospital-building" size={responsiveFontSize(2)} color={Colors.tertiary} />
      <Text style={styles.detailText}>Hospital: {item.hospital}</Text>
    </View>

    <View style={styles.detailRow}>
      <Icon name="alert" size={responsiveFontSize(2)} color={getUrgencyColor(item.urgency)} />
      <Text style={[styles.detailText, { color: getUrgencyColor(item.urgency) }]}>
        Urgency: {item.urgency}
      </Text>
    </View>

    <View style={styles.phoneRow}>
      <View style={styles.phoneTextWrapper}>
        <Icon name="phone" size={responsiveFontSize(2)} color={Colors.tertiary} />
        <Text style={styles.detailText}>Phone: {item.phone}</Text>
      </View>

      <TouchableOpacity onPress={() => {
        Clipboard.setString(item.phone);
        Platform.OS === 'android' && ToastAndroid.show('Phone number copied', ToastAndroid.SHORT);
      }}>
        <Icon name="content-copy" size={responsiveFontSize(2)} color={Colors.tertiary} />
      </TouchableOpacity>
    </View>

    <TouchableOpacity style={styles.contactButton} onPress={() => Linking.openURL(`tel:${item.phone}`)}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Icon name="phone" size={responsiveFontSize(2)} color="#fff" style={{ marginRight: 6 }} />
        <Text style={styles.contactButtonText}>Call</Text>
      </View>
    </TouchableOpacity>
  </View>
);


  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Blood Bank"  fabTitle={'Raise Requirement'} onFabPress={() => navigation.navigate('RaiseBloodRequirement')} />
      <View style={styles.container}>
        <Searchbar
          placeholder="Search by name"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.tertiary}
          placeholderTextColor={Colors.tertiary}
        />

        <FlatList
          showsVerticalScrollIndicator={false}
          data={filteredData}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          renderItem={renderItem}
          ListEmptyComponent={<Text style={styles.emptyText}>No Requiremnt details found.</Text>}
        />

      </View>

    </View>
  )
}

export default BloodBank

const styles = StyleSheet.create({
   container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
  },
   searchBar: {
    marginBottom: responsiveHeight(1.5),
    borderRadius: responsiveWidth(6),
    elevation: 2,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    fontWeight: '600',
    minHeight: responsiveHeight(4),
  },
  listContainer: {
    // padding: responsiveWidth(4),
    paddingBottom: responsiveHeight(4),
  },
    emptyText: {
    fontSize: responsiveFontSize(2),
    color: Colors.tertiary,
    textAlign: 'center',
    marginTop: responsiveHeight(10),
  },
 cardNew: {
  backgroundColor: '#f9f9f9',
  borderRadius: responsiveWidth(3),
  padding: responsiveWidth(4),
  marginBottom: responsiveHeight(2),
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 3,
  elevation: 3,
},

cardHeader: {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: responsiveHeight(1),
},

patientName: {
  fontSize: responsiveFontSize(2),
  fontWeight: 'bold',
  color: Colors.textPrimary || '#333',
},

badge: {
  paddingHorizontal: responsiveWidth(3),
  paddingVertical: responsiveHeight(0.5),
  borderRadius: responsiveWidth(5),
},

badgeText: {
  color: '#fff',
  fontSize: responsiveFontSize(1.6),
  fontWeight: '700',
},

detailRow: {
  flexDirection: 'row',
  alignItems: 'center',
  marginVertical: responsiveHeight(0.5),
},

detailText: {
  marginLeft: responsiveWidth(2),
  fontSize: responsiveFontSize(1.7),
  color: Colors.tertiary,
},

contactButton: {
  marginTop: responsiveHeight(1.5),
  backgroundColor: Colors.primary,
  paddingVertical: responsiveHeight(1),
  borderRadius: responsiveWidth(3),
  alignItems: 'center',
},

contactButtonText: {
  color: '#fff',
  fontSize: responsiveFontSize(1.7),
  fontWeight: '600',
},

phoneRow: {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginVertical: responsiveHeight(0.5),
},

phoneTextWrapper: {
  flexDirection: 'row',
  alignItems: 'center',
},


});