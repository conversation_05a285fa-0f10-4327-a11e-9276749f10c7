import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Share,
  Linking,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import MyHeader from '../../components/MyHeader';
import CustomButton from '../../components/CustomButton';
import Colors from '../../styles/Colors';
import CustomDateTimePicker from '../../components/CustomDateTimePicker';
import { Searchbar } from 'react-native-paper';

const mockReports = [
  {
    patientName: '<PERSON>',
    phone: '919123456789',
    id: '1',
    reports: [
      {
        id: '1',
        test: 'CBC',
        reportFile: 'https://vidwitha.com/pdf/20240914121906623102.pdf',
        date: '2025-07-01',
      },
      {
        id: '2',
        test: 'Vitamin D',
        reportFile: 'https://example.com/reports/john_vitamin.png',
        date: '2025-07-02',
      },
    ],
  },
  {
    patientName: '<PERSON> <PERSON>',
    phone: '919876543210',
    id: '2',
    reports: [
      {
        id: '1',
        test: 'CBC',
        reportFile: 'https://vidwitha.com/pdf/20240914121906623102.pdf',
        date: '2025-07-05',
      },
      {
        id: '2',
        test: 'Urine Test',
        reportFile: 'https://vidwitha.com/pdf/20240914121906623102.pdf',
        date: '2025-07-05',
      },
    ],
  },
];

const Reports = ({ navigation }) => {
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(new Date());
  const [filteredData, setFilteredData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');


  useEffect(() => {
    const filtered = mockReports
      .map((patient) => {
        const reports = patient.reports.filter((r) => {
          const date = new Date(r.date);
          return (!fromDate || date >= fromDate) && (!toDate || date <= toDate);
        });

        if (
          reports.length &&
          (patient.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            patient.phone.includes(searchQuery))
        ) {
          return { ...patient, reports };
        }

        return null;
      })
      .filter(Boolean); 

    setFilteredData(filtered);
  }, [fromDate, toDate, searchQuery]);

  const openFile = (reports) => {
    const url = reports.reportFile;
    const pdfName = reports.test || 'Report';
    // Linking.openURL(url);
    navigation.navigate('PDFViewer', { url, pdfName });
  };

  const shareFile = async (url) => {
    try {
      await Share.share({ message: `Report: ${url}`, url });
    } catch (err) {
      console.warn(err);
    }
  };


  const handleBatch = (action) => {
    filteredData.forEach((p) =>
      p.reports.forEach((r) =>
        action === 'download' ? openFile(r.reportFile) : shareFile(r.reportFile)
      )
    );
  };

  return (
    <View style={styles.container}>
      <MyHeader title="Reports" onBackPress={() => navigation.goBack()} />

         <View style={styles.searchBarContainer}>
            <Searchbar
                placeholder="Search here"
                onChangeText={setSearchQuery}
                value={searchQuery}
                style={styles.searchBar}
                inputStyle={styles.searchInput}
                iconColor={Colors.tertiary}
                placeholderTextColor={Colors.tertiary}
            />
        </View>

      <View style={styles.filterRow}>
        <CustomDateTimePicker
          placeholder="From Date"
          value={fromDate}
          setDate={setFromDate}
          iconName="calendar"
          mode="date"
          flex={1}
          maximumDate={new Date()}
        />
        <View style={{ width: responsiveWidth(2) }} />
        <CustomDateTimePicker
          placeholder="To Date"
          value={toDate}
          setDate={setToDate}
          iconName="calendar"
          mode="date"
          flex={1}
          maximumDate={new Date()}
        />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {filteredData.length === 0 ? (
          <Text style={styles.noData}>No reports found in selected range.</Text>
        ) : (
          // Replace inside your render logic in <ScrollView>
filteredData.map((patient) => (
  <View key={patient.id} style={styles.patientCard}>
    <Text style={styles.patientName}>{patient.patientName}</Text>

    {patient.reports.map((report, index) => (
      <View key={report.id} style={styles.reportRow}>
        <View style={styles.reportLeft}>
          <Text style={styles.serial}>{index + 1}.</Text>
          <View style={{ marginLeft: 8 }}>
            <Text style={styles.testText}>{report.test}</Text>
            <Text style={styles.dateText}>📅 {report.date}</Text>
          </View>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity onPress={() => openFile(report)} style={[styles.actionButton, {backgroundColor: `${Colors.viewButton}30`,}]}>
            <Icon name="eye" size={responsiveFontSize(1.8)} color={Colors.viewButton} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => shareFile(report.reportFile)}
            style={[styles.actionButton, { backgroundColor: `${Colors.success}30`, marginLeft: 16 }]}
          >
            <Icon name="share-variant" size={responsiveFontSize(1.8)} color={Colors.success} />
          </TouchableOpacity>
        </View>
      </View>
    ))}
  </View>
))

        )}
      </ScrollView>

      {/* <View style={styles.buttonRow}>
        <CustomButton
          title="Download All"
          width={responsiveWidth(42)}
          onPress={() => handleBatch('download')}
          color={Colors.white}
        />
        <CustomButton
          title="Share All"
          width={responsiveWidth(42)}
          onPress={() => handleBatch('share')}
          color={Colors.white}
        />
      </View> */}
    </View>
  );
};

export default Reports;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  searchBarContainer: {
      paddingHorizontal: responsiveWidth(4),
      backgroundColor: Colors.white,
      paddingTop: responsiveHeight(0.5),
  },
   searchBar: {
        borderRadius: responsiveWidth(6),
        elevation: 2,
    },
    searchInput: {
        fontSize: responsiveFontSize(1.6),
        color: Colors.primary,
        fontWeight: '600',
        minHeight: responsiveHeight(4),
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: responsiveWidth(4),
    marginTop: responsiveHeight(2),
  },
  scrollContent: {
    paddingHorizontal: responsiveWidth(4),
    paddingBottom: responsiveHeight(3),
  },
patientCard: {
  backgroundColor: '#f9f9f9',
  borderRadius: 12,
  padding: responsiveHeight(2),
  marginBottom: responsiveHeight(2),
  shadowColor: '#000',
  shadowOpacity: 0.1,
  shadowOffset: { width: 0, height: 2 },
  shadowRadius: 4,
  elevation: 3,
},

patientName: {
  fontSize: responsiveFontSize(2.1),
  fontWeight: 'bold',
  color: Colors.primary,
  marginBottom: responsiveHeight(1),
  borderBottomWidth: 1,
  borderColor: '#ddd',
  paddingBottom: 6,
},

reportRow: {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingVertical: responsiveHeight(1),
  borderBottomWidth: 1,
  borderBottomColor: '#eee',
},

reportLeft: {
  flexDirection: 'row',
  alignItems: 'flex-start',
  flex: 1,
},

serial: {
  fontSize: responsiveFontSize(1.8),
  fontWeight: '600',
  color: Colors.black,
},

testText: {
  fontSize: responsiveFontSize(1.9),
  fontWeight: '500',
  color: Colors.black,
},

dateText: {
  fontSize: responsiveFontSize(1.6),
  color: '#666',
  marginTop: 2,
},

actions: {
  flexDirection: 'row',
  alignItems: 'center',
},

  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
  },
  noData: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.grey,
    textAlign: 'center',
    marginTop: responsiveHeight(4),
  },
  actionButton:{
    padding: responsiveHeight(0.6),
    borderRadius: responsiveWidth(6),
    backgroundColor: Colors.viewButton,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
