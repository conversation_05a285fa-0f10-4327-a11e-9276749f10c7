import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import CustomButton from '../../components/CustomButton';
import CustomInput2 from '../../components/CustomInput2';
import CustomDateTimePicker from '../../components/CustomDateTimePicker';
import MyHeader from '../../components/MyHeader';

const RaisePickup = ({ navigation }) => {
    const [pickupDate, setPickupDate] = useState(null);
    const [numVials, setNumVials] = useState('');
    const [remarks, setRemarks] = useState('');

    const handleSubmit = () => {
        console.log({
        pickupDate,
        numVials,
        remarks,
        });
        // Add API logic later
    };

  return (
    <View style={{ flex: 1 }}>
      <MyHeader title="Raise Pickup" onBackPress={() => navigation.goBack()} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* <Text style={styles.heading}>Raise Pickup Request</Text> */}
        <View  style={styles.section}>
            <Text style={styles.label}>Pickup Schedule</Text>
            <CustomDateTimePicker
            placeholder="Select Date & Time"
            value={pickupDate}
            setDate={setPickupDate}
            iconName="calendar-clock"
            mode="date"
            />
        </View>
        <View style={styles.section}>
            <Text style={styles.label}>Number of Vials</Text>
            <CustomInput2
            label="Vials"
            value={numVials}
            onChangeText={setNumVials}
            icon="test-tube"
            placeholder="Enter number of vials"
            keyboardType="numeric"
            />
        </View>
        <View style={styles.section}>
            <Text style={styles.label}>Remarks</Text>
            <CustomInput2
            label="Remarks"
            value={remarks}
            onChangeText={setRemarks}
            icon="note-text-outline"
            placeholder="Add any remarks"
            />
        </View>

        <CustomButton title="Submit Pickup" color={Colors.white} onPress={handleSubmit} />
      </ScrollView>
    </View>
  )
}

export default RaisePickup


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
    backgroundColor: Colors.white,
    paddingTop: responsiveHeight(1),
  },
  heading: {
    fontSize: responsiveFontSize(2.4),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(2),
  },
  section: {
    marginBottom: responsiveHeight(1),
  },
  label: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: '500',
    color: Colors.black,
    marginBottom: responsiveHeight(0.4),
  },
});