import { View, Text, StyleSheet, Image } from 'react-native';
import React, { useState } from 'react';
import { TextInput } from 'react-native-paper';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../../styles/Colors';
import BottomLink from '../../components/BottomLink';
import CustomButton from '../../components/CustomButton';

const ForgotPassword = ({ navigation }) => {
     const [phoneNumber, setPhoneNumber] = useState('');

    const handleSendOTP = () => {
        // Add validation here if needed
        navigation.navigate('OtpVerification', { phoneNumber });
    };

  return (
    <View style={styles.container}>      
      <View style={styles.header}>
        {/* <MaterialCommunityIcons name="lock-reset" size={responsiveFontSize(7)} color={Colors.white} /> */}
        <View style={styles.logoContainer}>
            <MaterialCommunityIcons name="shield-lock" size={responsiveFontSize(8)} color={Colors.primary} style={styles.icon} />
        </View>
        <Text style={styles.headerTitle}>Forgot Password</Text>
        <Text style={styles.headerSubtitle}>Don't worry, we’ll help you reset it</Text>
      </View>

      {/* Form Card */}
      <View style={styles.formContainer}>
        <TextInput
          mode="outlined"
          label="Phone Number"
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          maxLength={10}
          keyboardType="phone-pad"
          placeholder="Enter your phone number"
          style={styles.input}
          left={<TextInput.Icon icon="phone" color={Colors.primary} />}
          outlineColor={Colors.primary}
          theme={{
            roundness: responsiveWidth(2),
            colors: {
              primary: Colors.primary,
              text: Colors.primary,
              placeholder: Colors.tertiary,
            },
          }}
        />

        <CustomButton title="Send OTP" onPress={handleSendOTP} color={Colors.white} />
        <View style={{marginTop: responsiveHeight(2), alignSelf: 'center'}}>
            <BottomLink text="Remembered your password? " subText="Login" subTextColor={Colors.primary} onPress={() => navigation.goBack()} />
        </View>
      </View>
    </View>
  )
}

export default ForgotPassword


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    backgroundColor: Colors.primary,
    borderBottomLeftRadius: responsiveWidth(10),
    borderBottomRightRadius: responsiveWidth(10),
    paddingTop: responsiveHeight(6),
    paddingBottom: responsiveHeight(4),
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.white,
    marginTop: responsiveHeight(1),
  },
  headerSubtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.white,
    opacity: 0.9,
    marginTop: responsiveHeight(0.5),
    textAlign: 'center',
    paddingHorizontal: responsiveWidth(10),
  },
  formContainer: {
    backgroundColor: Colors.white,
    marginTop: responsiveHeight(4),
    marginHorizontal: responsiveWidth(6),
    borderRadius: responsiveWidth(4),
    padding: responsiveWidth(5),
    shadowColor: Colors.primary,
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 5 },
    shadowRadius: 10,
    elevation: 6,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
    // backgroundColor: Colors.white,
  },
  icon: {
    backgroundColor: Colors.white,
    padding: responsiveWidth(5),
    borderRadius: responsiveWidth(15),
  },
  logo: {
    width: responsiveWidth(25),
    height: responsiveHeight(12),
    resizeMode: 'cover',
    borderRadius: responsiveWidth(3),
    borderWidth: 2,
    borderColor: Colors.primaryWithOpacity,
  },
  input: {
    backgroundColor: '#fff',
    fontSize: responsiveFontSize(1.8),
    marginBottom: responsiveHeight(3),
  },
});