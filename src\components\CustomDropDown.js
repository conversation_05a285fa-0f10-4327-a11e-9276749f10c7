import React, {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {responsiveFontSize, responsiveHeight, responsiveWidth,} from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'
import {Dropdown} from 'react-native-element-dropdown';
import Colors from '../styles/Colors';

const CustomDropDown = ({title, uprLabel, value, setValue, gap, iconName, data, placeholder, dropdownPosition = 'auto', disabled = false,}) => {
  const [isFocus, setIsFocus] = useState(false);
  const [tempValue, setTempValue] = useState(null);
  
  const renderLabel = () => {
        if (value || isFocus) {
            return (
                <Text style={[styles.uprLabel, isFocus && { color: Colors.primary }, { backgroundColor: 'white' }]}>
                    {uprLabel}
                </Text>
            );
        }
        return null;
    };

 const handleFocus = () => {
    setTempValue(value); // save original value
    setIsFocus(true);
  };

  const handleBlur = () => {
    setIsFocus(false);
    // if nothing selected during dropdown open, restore previous
    if (!value && tempValue) {
      setValue(tempValue);
    }
  };

  return (
   <View style={[styles.main, { gap: gap }, { zIndex: isFocus ? 10 : 1 }, ]}>
      {/* <Text style={styles.label}>{title}</Text> */}
      <View  pointerEvents={disabled ? 'none' : 'auto'} style={[styles.inputContainer, disabled && { borderColor: '#d8d8d8' },]}>
        {renderLabel()}
        <View style={[styles.icon, disabled && { backgroundColor: '#f0f0f0' },]}>
          <MaterialCommunityIcons name={iconName} size={responsiveFontSize(2.5)} color={disabled ? Colors.tertiary : Colors.primaryDropDownOpacity} />
        </View>
        <Dropdown
          disabled={disabled}
          style={[styles.dropdown,]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={[styles.selectedTextStyle, disabled && { color: '#a0a0a0' }]}
          iconStyle={styles.iconStyle}
          data={data}
          maxHeight={responsiveHeight(15)} // Use responsive height instead of fixed value
          labelField="label"
          valueField="value"
          placeholder={placeholder}
          value={isFocus ? null : value} // 💡 KEY LINE
          dropdownPosition={dropdownPosition}
          // onFocus={() => setIsFocus(true)}
          // onBlur={() => setIsFocus(false)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={item => {
            setValue(item.value);
            setIsFocus(false)
          }}
          // Add these properties to improve dropdown behavior
          search={false} // Disable search to save space
          activeColor={Colors.primaryWithExtraOpacity} // Highlight color for selected item
          containerStyle={styles.dropdownContainer} // Custom container style
          renderItem={(item, selected) => (
            <View style={[styles.dropdownItem, selected && styles.selectedItem]}>
              <Text style={[styles.dropdownItemText, selected && styles.selectedItemText]}>
                {item.label}
              </Text>
            </View>
          )}
        />
      </View>
    </View>
  )
}

export default CustomDropDown

const styles = StyleSheet.create({
  main: {
    marginBottom: responsiveHeight(1.5),
  },
  label: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
  },
  icon: {
    // backgroundColor: Colors.error,
    padding: responsiveWidth(2.3),
  },
  inputContainer: {
    width: '100%',
    borderColor: Colors.primaryDropDownOpacity,
    borderWidth: 1,
    borderRadius: responsiveWidth(2),
    flexDirection: 'row',
    alignItems: 'center',
    // overflow: 'hidden',

  },
  dropdown: {
    flex: 1,
    paddingRight: responsiveWidth(1),
    marginLeft: responsiveWidth(1.6),
  },
  placeholderStyle: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primaryDropDownOpacity,
  },
  selectedTextStyle: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.black,
    fontWeight: '500',
  },
  iconStyle: {
    width: responsiveWidth(4),
    height: responsiveHeight(2.5),
    paddingRight: responsiveWidth(7),
  },
  // New styles for dropdown items
  dropdownContainer: {
    borderRadius: responsiveWidth(2),
    borderWidth: 1,
    borderColor: Colors.primaryWithExtraOpacity,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    backgroundColor: Colors.white,
    // position: 'absolute',
    zIndex: 1000,
    marginTop: 0, // Ensure no top margin pushes it up
    maxHeight: responsiveHeight(25), // Limit maximum height to 25% of screen height
  },
  dropdownItem: {
    paddingHorizontal: responsiveWidth(3),
    paddingVertical: responsiveHeight(1),
    borderBottomWidth: 1,
    borderBottomColor: Colors.primaryWithExtraOpacity,
    height: responsiveHeight(5), // Fixed height for each item
    justifyContent: 'center', // Center content vertically
    
  },
  selectedItem: {
    backgroundColor: Colors.primaryWithExtraOpacity,
  },
  dropdownItemText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
  },
  selectedItemText: {
    color: Colors.primary,
    fontWeight: '500',
  },

  uprLabel: {
        position: 'absolute',
        backgroundColor: Colors.white,
        left: responsiveWidth(2.3),
        top: -responsiveHeight(1.2),
        color: Colors.primaryDropDownOpacity,
        paddingHorizontal: responsiveWidth(1.3),
        fontSize: responsiveFontSize(1.4),
        zIndex: 2001,
    },
});