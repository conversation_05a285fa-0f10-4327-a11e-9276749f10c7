import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import MyHeader from '../../components/MyHeader';
import Colors from '../../styles/Colors';
import CustomDropDown from '../../components/CustomDropDown';
import CustomButton from '../../components/CustomButton';
import ImageWithFallback from '../../components/ImageWithFallback';
import { Linking } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

const mockLabs = [
  {
    id: 'lab1',
    name: 'Dr. Path Lab',
    image: 'https://picsum.photos/seed/lab1/200/200',
    tests: [
      { name: 'CBC TEST', SLDCode: 'SLDC126', labTestCode: 'DP-BS-001', price: 400 },
      { name: 'Urine Test', SLDCode: 'USLD014', labTestCode: 'DP-CH-002', price: 650 },
    ],
  },
  {
    id: 'lab2',
    name: 'Health First Diagnostics',
    image: 'https://picsum.photos/seed/lab2/200/200',
    tests: [
      { name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020', labTestCode: 'HF-BS-789', price: 420 },
      { name: 'Cholesterol', SLDCode: 'T002', labTestCode: 'HF-CH-120', price: 600 },
    ],
  },
  {
    id: 'lab3',
    name: 'QuickTest Labs',
    image: 'https://picsum.photos/seed/lab3/200/200',
    tests: [
      { name: 'Blood Sugar', SLDCode: 'T001', labTestCode: 'QT-BS-305', price: 410 },
      { name: 'Cholesterol', SLDCode: 'T002', labTestCode: 'QT-CH-412', price: 590 },
    ],
  },
  {
    id: 'lab4',
    name: 'Medix Diagnostics',
    image: 'https://picsum.photos/seed/lab4/200/200',
    tests: [
      { name: 'CBC TEST', SLDCode: 'SLDC126', labTestCode: 'MD-CBC-001', price: 390 },
      { name: 'Urine Test', SLDCode: 'USLD014', labTestCode: 'MD-UT-045', price: 670 },
    ],
  },
  {
    id: 'lab5',
    name: 'Trust Lab Care',
    image: 'https://picsum.photos/seed/lab5/200/200',
    tests: [
      { name: 'Blood Sugar', SLDCode: 'T001', labTestCode: 'TL-BS-100', price: 430 },
      { name: 'Cholesterol', SLDCode: 'T002', labTestCode: 'TL-CH-101', price: 580 },
    ],
  },
  {
    id: 'lab6',
    name: 'AccuHealth Lab',
    image: 'https://picsum.photos/seed/lab6/200/200',
    tests: [
      { name: 'CBC TEST', SLDCode: 'SLDC126', labTestCode: 'AH-CBC-007', price: 410 },
      { name: 'Urine Test', SLDCode: 'USLD014', labTestCode: 'AH-UT-208', price: 640 },
    ],
  },
  {
    id: 'lab7',
    name: 'SmartPath Labs',
    image: 'https://picsum.photos/seed/lab7/200/200',
    tests: [
      { name: 'Blood Sugar', SLDCode: 'T001', labTestCode: 'SP-BS-888', price: 395 },
      { name: 'Cholesterol', SLDCode: 'T002', labTestCode: 'SP-CH-909', price: 610 },
    ],
  },
  {
    id: 'lab8',
    name: 'Metro Labs',
    image: 'https://picsum.photos/seed/lab8/200/200',
    tests: [
      { name: 'CBC TEST', SLDCode: 'SLDC126', labTestCode: 'ML-CBC-099', price: 405 },
      { name: 'Urine Test', SLDCode: 'USLD014', labTestCode: 'ML-UT-100', price: 660 },
    ],
  },
  {
    id: 'lab9',
    name: 'Alpha Diagnostics',
    image: 'https://picsum.photos/seed/lab9/200/200',
    tests: [
      { name: 'Blood Sugar', SLDCode: 'T001', labTestCode: 'AD-BS-222', price: 400 },
      { name: 'Cholesterol', SLDCode: 'T002', labTestCode: 'AD-CH-333', price: 620 },
    ],
  },
  {
    id: 'lab10',
    name: 'GreenLab Services',
    image: 'https://picsum.photos/seed/lab10/200/200',
    tests: [
      { name: 'CBC TEST', SLDCode: 'SLDC126', labTestCode: 'GL-CBC-002', price: 395 },
      { name: 'Urine Test', SLDCode: 'USLD014', labTestCode: 'GL-UT-010', price: 645 },
    ],
  },
];


const paymentMethods = [
  { label: 'Cash', value: 'Cash' },
  { label: 'Card', value: 'Card' },
  { label: 'UPI', value: 'UPI' },
];



const ChooseLab = ({ route, navigation }) => {
    const { item: patient } = route.params;
    const [selectedLabs, setSelectedLabs] = useState([]);
    const [selectedBookingLab, setSelectedBookingLab] = useState(null);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('Cash');
    const [filterType, setFilterType] = useState(patient?.collectionType);
    const [sampleCollected, setSampleCollected] = useState(patient?.collectionType === 'Draw' ? true : false)
    const [appliedCoupon, setAppliedCoupon] = useState(null);

    console.log('Choose lab page patient detail', patient)
    console.log('selected Lab:', selectedLabs)

    const handleFilter = (type) => {
        setFilterType(prev => (prev === type ? null : type));
    };

    const toggleLabSelection = (lab) => {
        const exists = selectedLabs.find((l) => l.id === lab.id); 
        if (exists) {
            setSelectedLabs((prev) => prev.filter((l) => l.id !== lab.id)); // Deselect if already selected
        } else {
            if (selectedLabs.length >= 3) {
                alert('You can select up to 3 labs only.');
            return;
            }
           
            setSelectedLabs((prev) => [...prev, lab]);  // Add to selection
        }
    };

    const renderLabItem = ({ item }) => {
        const isSelected = selectedLabs.some((l) => l.id === item.id);
        return (
            <TouchableOpacity onPress={() => toggleLabSelection(item)} style={styles.labCard}>
                <Image source={{ uri: item.image }} style={styles.labImage} />
                <Text numberOfLines={1} style={[styles.labName,{ maxWidth:responsiveWidth(20), textAlign:'center', textTransform: 'uppercase'}]}>{item.name}</Text>
                {isSelected  && 
                <View style={{position: 'absolute', right:responsiveWidth(4), top: responsiveHeight(0.5),backgroundColor: Colors.white, borderRadius: responsiveWidth(2)}}>
                    <Icon name="check-circle" size={18} color={Colors.green} />
                </View>
                }
            </TouchableOpacity>
        )
    };

  return (
     <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader
        title="Choose Lab"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView contentContainerStyle={[styles.container, { flexGrow: 1 }]}>
        <Text style={styles.patientName}>Patient: <Text style={{color: Colors.primary, fontSize: responsiveFontSize(1.8),}}>{patient.name}</Text></Text>

        <View style={styles.testWrap}>
          <Text style={styles.sectionTitle}>Tests:</Text>
          {patient.testNames?.map((test, index) => (
            <View key={index} style={styles.testChip}>
              <Icon name="flask-outline" size={responsiveFontSize(1.5)} color={Colors.primary} />
              <Text style={styles.testText}>{test.name} <Text style={{color: Colors.green}}>({test.SLDCode})</Text></Text>
            </View>
          ))}
        </View>
        
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
          <Text style={styles.collectionDate}>Collection Date: <Text style={{color: Colors.primary, fontSize: responsiveFontSize(1.6)}}>{patient?.date} ({sampleCollected ? 'Collected' : 'Not Collected Yet'})</Text></Text>
        </View>

        <View style={{backgroundColor: Colors.background , marginVertical: responsiveHeight(0)}}>
        <Text style={styles.sectionTitle}>Sample Collection Status</Text>  
        <View style={{flexDirection: 'row', justifyContent: 'space-between',}}>
          <View>
            <Text style={styles.sectionTitle}>Collection Type:</Text>
            <View style={[styles.filterRow,]}>
              <View
                style={[
                  styles.filterButton,
                  filterType === 'Draw' && styles.selectedFilter,
                ]}
                // onPress={() => handleFilter('Draw')}
              >
                <Icon name="needle" size={responsiveFontSize(2)} color={Colors.primary} />
                <Text style={styles.filterText}>{filterType}</Text>
              </View>
            </View>
          </View>

          {(filterType === 'Draw & Pickup' || filterType === 'Pickup' || filterType === 'Draw') &&
            <View style={{alignItems: 'flex-end'}}>
              <Text style={[styles.sectionTitle,]}>Current Status:</Text>
              <View style={[styles.collectionStatus,]}>
                <Text style={styles.currentStatus}>{`${filterType != 'Draw' ? 'Pending, accepted by phlebo, sample collected by phlebo, submited to center' : 'Collected in Center'}`}</Text>
              </View>
            </View>
          }
        </View>
        </View>

        {filterType != 'Draw' &&
          <View>
              <Text style={styles.sectionTitle}>Assigned Phlebo Details:</Text>
              <View style={styles.phleboCard}>
                <View style={styles.phleboDetail}>
                  <ImageWithFallback style={styles.phleboImage} iconName="account" iconSize={3.5} />
                  <View>
                    <Text style={styles.phleboName}>Name: Kamlesh Patel</Text>
                    <Text style={styles.phleboName}>Phone: **********</Text>
                  </View>
                </View>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${**********}`)} style={[styles.phleboDetail, {backgroundColor: Colors.green, padding: responsiveWidth(2), borderRadius: responsiveWidth(4), gap: responsiveWidth(1)}]}>
                    <Icon name="phone" size={responsiveFontSize(2)} color="#fff"/>
                    <Text style={styles.contactButtonText}>Call</Text>
                </TouchableOpacity>
              </View>
          </View>
        }

      {/* {sampleCollected &&  */}
       <>
        <Text style={styles.sectionTitle}>Choose Labs:</Text>
        <View style={{backgroundColor: Colors.background || '#f9f9f9', justifyContent: 'center'}}>
            <FlatList
                data={mockLabs}
                keyExtractor={(item) => item.id}
                renderItem={renderLabItem}
                contentContainerStyle={{ paddingVertical: responsiveHeight(2) }}
                horizontal
                showsHorizontalScrollIndicator={false}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                    <Icon name="flask-empty-outline" size={responsiveFontSize(4)} color={Colors.tertiary} />
                    <Text style={styles.emptyText}>No labs available</Text>
                    </View>
                }
            />
        </View>
        </>
        {/* } */}

        {selectedLabs.length > 0 && (
            <View style={styles.comparisonContainer}>
                <Text style={styles.sectionTitle}>Price Comparison:</Text>

                {/* Table Header */}
                <View style={styles.row}>
                  <Text style={[styles.cell, styles.headerCell, styles.comnText, { flex: 1.5, color: Colors.white  }]}>Test SLD</Text>
                  {selectedLabs.map((lab) => (
                      <Text key={lab.id} style={[styles.cell, styles.headerCell, styles.comnText,  {color: Colors.white}]}>
                      {lab.name}
                      </Text>
                  ))}
                </View>

                {/* Test Rows */}
                {patient.testNames.map((test) => (
                <View key={test.SLDCode} style={styles.row}>
                    <Text style={[styles.cell, styles.comnText, { flex: 1.5 }]}>
                      {test.name} ({test.SLDCode})
                    </Text>
                    {selectedLabs.map((lab) => {
                    const labTest = lab?.tests?.find((t) => t.SLDCode === test.SLDCode);
                    return (
                        <Text key={`${lab.id}-${test.SLDCode}`} style={[styles.cell,  styles.comnText,]}>
                        {labTest ? `₹${labTest.price}\n(${labTest.labTestCode})` : 'N/A'}
                        </Text>
                    );
                    })}
                </View>
                ))}

                {/* Total price Row */}
                <View style={[styles.row, { backgroundColor: '#f1f1f1' }]}>
                  <Text style={[styles.cell,  styles.comnText, { flex: 1.5, fontWeight: 'bold' }]}>Total</Text>
                  {selectedLabs.map((lab) => {
                    const total = patient.testNames.reduce((sum, test) => {
                      const labTest = lab.tests.find((t) => t.SLDCode === test.SLDCode);
                      return sum + (labTest?.price || 0);
                    }, 0);
                    return (
                      <Text key={`${lab.id}-total`} style={[styles.cell,  styles.comnText, { fontWeight: 'bold' }]}>
                        ₹{total}
                      </Text>
                    );
                  })}
                </View>

                {/* Book Buttons */}
              <View style={[styles.row, { backgroundColor: '#fff', borderTopWidth: 0 }]}>
                <View style={{ flex: 1.5 }} /> {/* Empty space to align with first column */}
                {selectedLabs.map((lab) => {
                  const total = patient.testNames.reduce((sum, test) => {
                    const labTest = lab.tests.find((t) => t.SLDCode === test.SLDCode);
                    return sum + (labTest?.price || 0);
                  }, 0);

                  const isDisabled = total === 0;
                  return (
                    <View key={`${lab.id}-book`} style={[styles.cell, styles.bookCell]}>
                      {/* {total > 0 && ( */}
                        <TouchableOpacity
                          disabled={isDisabled}
                          style={[styles.bookButton, isDisabled && styles.disabledButton, selectedBookingLab?.lab?.id === lab.id && styles.selectedBookButton,]}
                          onPress={() => {
                            console.log(patient,lab,)
                            if (!isDisabled) {
                              setSelectedBookingLab({ lab, total });
                              setSelectedPaymentMethod('Cash');
                            }
                          }}
                        >
                          <Text style={[styles.bookButtonText, isDisabled && styles.disabledButtonText, selectedBookingLab?.lab?.id === lab.id && styles.selectedBookButtonText,]}>Book</Text>
                        </TouchableOpacity>
                      {/* )} */}
                    </View>
                  );
                })}
              </View>
            </View>
        )}

        {selectedLabs.length > 0 && selectedBookingLab && (
          <View style={styles.bookingSection}>
              <Text style={[styles.sectionTitle, {marginVertical: responsiveHeight(0)}]}>Coupon:</Text>
              <TouchableOpacity onPress={()=> navigation.navigate('Coupon')} style={styles.couponContainer}>
                <View style={styles.couponLeft}>
                    <Icon name="ticket-confirmation-outline" size={responsiveFontSize(3)} color={Colors.primary} />
                    <Text style={styles.couponText}>Apply Coupon</Text>
                </View>
                <View  style={styles.couponRight}>
                  <Text style={styles.viewOffer}>View Offers</Text>
                  <Icon name="chevron-right" size={responsiveFontSize(2)} color={Colors.primary} />
                </View>
              </TouchableOpacity>

              {appliedCoupon && (
                <View style={styles.appliedCouponBox}>
                  <View style={styles.couponLeft}>
                    <View style={{padding: responsiveWidth(2), backgroundColor: Colors.greenLight, borderWidth: 1, borderColor: Colors.success, borderStyle: 'dashed',}}>
                      <Text style={[styles.couponText, {color: Colors.black}]}>WELCOME50</Text>
                    </View>
                    <Text style={[styles.couponText, {color: Colors.success}]}>Saved ₹50</Text>
                  </View>
                  <View style={styles.couponRight}>
                    <TouchableOpacity onPress={() => setAppliedCoupon(null)}>
                      <Text style={[styles.couponText, {color: Colors.error}]}>Remove</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
          </View>
        )}

        {/* Use your custom dropdown here */}
        {selectedLabs.length > 0 && selectedBookingLab && (
          <View style={styles.bookingSection}>
            <Text style={[styles.sectionTitle, {marginBottom: responsiveHeight(2)}]}>Payment Method:</Text>
            <CustomDropDown
              uprLabel="Payment Method"
              iconName="credit-card-outline"
              value={selectedPaymentMethod}
              setValue={setSelectedPaymentMethod}
              data={paymentMethods}
              placeholder="Payment Method"
              dropdownPosition='top'
            />
          </View>
        )}

        {/* Total price and selected lab */}
        {selectedLabs.length > 0 && selectedBookingLab && (
          <View style={styles.bookingSection}>
            <Text style={styles.bookingLabel}>
              Booking for: <Text style={{ fontWeight: 'bold', color: Colors.green, fontSize: responsiveFontSize(1.8) }}>{selectedBookingLab.lab.name}</Text>
            </Text>
            <View style={{paddingHorizontal: responsiveWidth(2), marginBottom: responsiveHeight(0)}}>
                        <View style={styles.chargeRow}>
                          <Text style={styles.leftTextRow}>Subtotal:</Text>
                          <Text style={styles.rightTextRow}>₹{selectedBookingLab.total}</Text>
                        </View>
                        <View style={styles.chargeRow}>
                          <Text style={styles.leftTextRow}>Collection Charge:</Text>
                          <Text style={styles.rightTextRow}>₹200</Text>
                        </View>
                        <View style={styles.totalRow}>
                          <Text style={styles.totalText}>Grand Total:</Text>
                          <Text style={styles.totalAmount}>₹{selectedBookingLab.total}</Text>
                        </View>
            </View>
          </View>
        )}

      </ScrollView>
        {selectedLabs.length > 0 && selectedBookingLab && (
          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: responsiveWidth(4), marginVertical: responsiveHeight(2), marginBottom: responsiveHeight(4), gap: responsiveWidth(6), elevation:2,}}>
            <View>
              <Text style={{color: Colors.black, fontSize: responsiveFontSize(1.6)}}>Payable Amount</Text>
              <Text style={[styles.totalText, {color: Colors.primary, fontWeight: 'bold', fontSize: responsiveFontSize(2)}]}>₹{selectedBookingLab.total}</Text>
            </View>
            <TouchableOpacity
              style={[styles.bookButton, { flex:1 ,alignSelf: 'center', marginTop: responsiveHeight(0), borderRadius: responsiveWidth(6) }]}
              onPress={() => {
                // navigation.navigate('BookingScreen', {
                //   patient,
                //   lab: selectedBookingLab.lab,
                //   tests: patient.testNames,
                //   total: selectedBookingLab.total,
                //   paymentMethod: selectedPaymentMethod,
                // });
                navigation.reset({
                index: 0,
                routes: [{name: 'Main'}],
                })
                // setSelectedBookingLab(null);
              }}
            >
              <Text style={[styles.bookButtonText, { textAlign: 'center',fontSize: responsiveFontSize(1.8), paddingHorizontal: responsiveWidth(2), paddingVertical: responsiveHeight(0.8), fontWeight: 'bold'}]}>PROCEED</Text>
            </TouchableOpacity>
          </View>
        )}

    </View>
  )
}

export default ChooseLab


const styles = StyleSheet.create({
  container: {
    paddingHorizontal: responsiveWidth(4),
    paddingVertical: responsiveHeight(1),
  },
  patientName: {
    fontSize: responsiveFontSize(1.5),
    fontWeight: 'bold',
    marginBottom: responsiveHeight(1),
    color: Colors.black,
  },
  collectionDate:{
    fontSize: responsiveFontSize(1.5),
    fontWeight: '600',
    color: Colors.black,
    marginVertical: responsiveHeight(1),
  },
  sectionTitle: {
    fontSize: responsiveFontSize(1.5),
    fontWeight: '600',
    color: Colors.black,
    marginVertical: responsiveHeight(1),
  },
  testWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: responsiveHeight(1),
  },
  testChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primaryWithExtraOpacity,
    paddingHorizontal: responsiveWidth(1.5),
    paddingVertical: responsiveHeight(0.4),
    borderRadius: responsiveWidth(4),
    marginRight: responsiveWidth(0.6),
    marginBottom: responsiveHeight(0.6),
  },
  testText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    marginLeft: responsiveWidth(1),
  },
  labCard: {
    minWidth: responsiveWidth(24),
    // flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white || '#f9f9f9',
    borderRadius: responsiveWidth(2),
    padding: responsiveWidth(2),
    gap: responsiveHeight(1),
    marginRight: responsiveWidth(3),
    elevation: 2,
  },
  
  labImage: {
    width: responsiveWidth(16),
    height: responsiveWidth(16),
    borderRadius: responsiveWidth(3),
  },
  labName: {
    fontSize: responsiveFontSize(1.4),
    fontWeight: 'bold',
    color: `${Colors.black}99`,
  },
  chooseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 'auto',
  },
  chooseText: {
    color: Colors.white,
    fontWeight: '500',
    marginRight: 5,
    fontSize: responsiveFontSize(1.6),
  },
  emptyContainer: {
  alignItems: 'center',
  justifyContent: 'center',
  flex: 1,
  width: responsiveWidth(100),
},
emptyText: {
  fontSize: responsiveFontSize(1.8),
  color: Colors.tertiary,
  marginTop: responsiveHeight(1),
},

comparisonContainer: {
  marginTop: responsiveHeight(2),
  backgroundColor: '#f4f4f4',
  borderRadius: responsiveWidth(2),
  overflow: 'hidden',
},
row: {
  flexDirection: 'row',
},
cell: {
  flex: 1,
  padding: responsiveHeight(1),
  borderWidth: 0.5,
  borderColor: '#ddd',
  // textAlign: 'center',
  // fontSize: responsiveFontSize(1.4),
  // color: Colors.black
},
comnText: {
  fontSize: responsiveFontSize(1.4),
  color: Colors.black,
  textAlign: 'center',
},
headerCell: {
  backgroundColor: '#e0e0e0',
  fontWeight: 'bold',
  backgroundColor: Colors.primary
},
bookCell: {
  borderWidth: 0, // Remove border for clean look
  justifyContent: 'center',
  alignItems: 'center',
},

bookButton: {
  backgroundColor: Colors.primary,
  paddingVertical: responsiveHeight(0.6),
  paddingHorizontal: responsiveWidth(2.5),
  borderRadius: responsiveWidth(4),
  elevation: 2, // optional for subtle shadow
},

bookButtonText: {
  color: Colors.white,
  fontWeight: '600',
  fontSize: responsiveFontSize(1.5),
},
disabledButton: {
  backgroundColor: '#ccc',
  elevation: 0,
},

disabledButtonText: {
  color: '#666',
},
bookingSection: {
    padding: responsiveWidth(3),
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(2),
    marginVertical: responsiveHeight(1),
    elevation: 3,
  },
  bookingLabel: {
    fontSize: responsiveFontSize(1.6),
    marginBottom: responsiveHeight(1.5),
  },

  selectedBookButton: {
    backgroundColor: Colors.green, // or any highlight color
    // borderWidth: 2,
    // borderColor: Colors.darkGreen || '#0a8a2a',
  },

selectedBookButtonText: {
  color: Colors.white,
  fontWeight: 'bold',
},

filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: responsiveHeight(1),
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    backgroundColor: Colors.white,
    paddingVertical: responsiveHeight(0.6),
    paddingHorizontal: responsiveWidth(2.5),
    borderRadius: responsiveWidth(5),
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  selectedFilter: {
    backgroundColor: Colors.primaryWithExtraOpacity,
  },
  filterText: {
    color: Colors.primary,
    fontSize: responsiveFontSize(1.6),
  },

  collectionStatus:{
    backgroundColor: `${Colors.green}10`,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    paddingVertical: responsiveHeight(0.8),
    paddingHorizontal: responsiveWidth(2.5),
    borderRadius: responsiveWidth(5),
    borderWidth: 1,
    borderColor: Colors.green,
  },
  currentStatus:{
    color: Colors.green,
    fontSize: responsiveFontSize(1.6),
    fontWeight: 'bold'
  },
  phleboCard:{
    backgroundColor: `${Colors.green}10`,
    padding: responsiveWidth(2),
    borderRadius: responsiveWidth(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 0.8,
    borderColor: Colors.green,
  },
  phleboDetail:{
    flexDirection: 'row',
    alignItems: 'center',
  },
  phleboName:{
    fontSize: responsiveFontSize(1.6),
    color: Colors.black,
    
  },
  phleboImage: {
    width: responsiveWidth(8),
    height: responsiveWidth(8),
    borderRadius: responsiveWidth(4),
    marginRight: responsiveWidth(2),
  },
  contactButtonText: {
  color: '#fff',
  fontSize: responsiveFontSize(1.7),
  fontWeight: '600',
},
  couponContainer: {
    backgroundColor: Colors.white,
    // borderWidth: 1,
    // borderColor: Colors.primary,
    padding: responsiveWidth(2),
    // borderRadius: responsiveWidth(2),
    marginTop: responsiveHeight(1),
    // elevation: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  couponText:{
    fontSize: responsiveFontSize(1.8),
    color: Colors.primary,
    fontWeight: 'bold',
  },
  viewOffer:{
    fontSize: responsiveFontSize(1.4),
    color: Colors.primary,
    fontWeight: 'bold',
  },
  couponLeft:{
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(2.5),
  },
  couponRight:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(0.5),
  },

  appliedCouponBox: {
    padding: responsiveHeight(1.5),
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(1),
    borderColor: `${Colors.success}60`,
    borderWidth: 1,
    marginTop: responsiveHeight(1),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
couponDesc: {
  fontSize: responsiveFontSize(1.7),
  color: '#555',
  marginTop: responsiveHeight(0.5),
},
validTill: {
  fontSize: responsiveFontSize(1.6),
  color: '#999',
  marginTop: responsiveHeight(0.5),
},
  chargeRow:{
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: responsiveHeight(0.5)
  },
  leftTextRow:{
      fontSize: responsiveFontSize(1.4),
      color: Colors.tertiary,
    },
    rightTextRow:{ 
      fontSize: responsiveFontSize(1.6),
      color: Colors.black,
      fontWeight: '600',
    },
    totalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: responsiveHeight(1),
        borderTopWidth: 1,
        borderColor: '#ccc',
        paddingTop: responsiveHeight(0.8),
      },
      totalText: {
        fontSize: responsiveFontSize(1.7),
        fontWeight: 'bold',
        color: Colors.black,
      },
      totalAmount: {
        fontSize: responsiveFontSize(1.8),
        fontWeight: '600',
        color: Colors.green,
      },

});