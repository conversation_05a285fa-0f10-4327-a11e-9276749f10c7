import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Searchbar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';


const testList = [
  {
    id: '1',
    name: 'CBC TEST',
    sldCode: 'SLDC126',
    organ: 'Blood',
    reportTime: '24 hrs',
    container: 'EDTA Tube',
    status: 'Active',
  },
  {
    id: '2',
    name: 'Liver Function Test',
    sldCode: 'SLD234',
    organ: 'Liver',
    reportTime: '2 days',
    container: 'SST Tube',
    status: 'Active',
  },
  {
    id: '3',
    name: 'Urine Test',
    sldCode: 'USLD014',
    organ: 'Urinary Tract',
    reportTime: '12 hrs',
    container: 'Sterile Container',
    status: 'Pending',
  },
];

const TestAcknowledge = ({ navigation }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [tests, setTests] = useState(testList);

    const filteredTests = tests.filter(
        (item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.sldCode.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const handleAcknowledge = (id) => {
        setTests((prev) =>
        prev.map((test) =>
            test.id === id ? { ...test, status: 'Acknowledged' } : test
        )
        );
    };

    const renderItem = ({ item }) => (
        <View style={styles.card}>
            <View style={styles.rowBetween}>
                <Text style={styles.testName}>{item.name}</Text>
                <Text
                style={[
                    styles.status,
                    { color: item.status === 'Active' ? Colors.green : Colors.error },
                ]}
                >
                {item.status}
                </Text>
            </View>
            <View style={styles.detailRow}>
                <Icon name="barcode" size={14} color={Colors.primary} />
                <Text style={styles.detailText}>SLD Code: {item.sldCode}</Text>
            </View>
            <View style={styles.detailRow}>
                <Icon name="heart-pulse" size={14} color={Colors.primary} />
                <Text style={styles.detailText}>Organ: {item.organ}</Text>
            </View>
            <View style={styles.detailRow}>
                <Icon name="clock-outline" size={14} color={Colors.primary} />
                <Text style={styles.detailText}>Report Time: {item.reportTime}</Text>
            </View>
            <View style={styles.detailRow}>
                <Icon name="flask-outline" size={14} color={Colors.primary} />
                <Text style={styles.detailText}>Container: {item.container}</Text>
            </View>
    
            {/* View Button */}
            <TouchableOpacity
                style={styles.viewButton}
                onPress={() => {
                    navigation.navigate('ViewTestAcknowledgeDetails', { test: item });
                }}
            >
                <Text style={styles.viewButtonText}>View</Text>
            </TouchableOpacity>
        </View>
    );
        
  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Test Acknowledgement" onBackPress={() => navigation.goBack()} />

      <View style={styles.container}>
        <Searchbar
          placeholder="Search by test name or SLD code"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.tertiary}
          placeholderTextColor={Colors.tertiary}
        />

        <FlatList
          showsVerticalScrollIndicator={false}
          data={filteredTests}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={{ paddingBottom: responsiveHeight(10) }}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No tests found</Text>
          }
        />
      </View>
    </View>
  )
}

export default TestAcknowledge



const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
  },
  searchBar: {
    marginBottom: responsiveHeight(1.5),
    borderRadius: responsiveWidth(6),
    elevation: 2,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    fontWeight: '600',
    minHeight: responsiveHeight(4),
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(1.5),
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 3,
  },
  testName: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  status: {
    fontSize: responsiveFontSize(1.5),
    fontWeight: 'bold',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: responsiveHeight(0.5),
  },
  detailText: {
    fontSize: responsiveFontSize(1.5),
    marginLeft: 6,
    color: Colors.tertiary,
  },
  acknowledgeButton: {
    backgroundColor: Colors.primary,
    paddingVertical: responsiveHeight(1),
    marginTop: responsiveHeight(1.5),
    borderRadius: responsiveWidth(2),
    alignItems: 'center',
  },
  acknowledgeText: {
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: responsiveFontSize(1.6),
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: responsiveHeight(0.8),
  },
  emptyText: {
    textAlign: 'center',
    marginTop: responsiveHeight(4),
    color: Colors.tertiary,
    fontSize: responsiveFontSize(1.6),
  },

  viewButton: {
  alignSelf: 'flex-end',
  marginTop: responsiveHeight(1.5),
  backgroundColor: Colors.primary,
  paddingHorizontal: responsiveWidth(4),
  paddingVertical: responsiveHeight(0.8),
  borderRadius: responsiveWidth(2),
  elevation: 2,
},

viewButtonText: {
  color: Colors.white,
  fontSize: responsiveFontSize(1.6),
  fontWeight: 'bold',
},

});