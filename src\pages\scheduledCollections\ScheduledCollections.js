import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Searchbar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';

const mockData = [
  {
    id: '1',
    name: '<PERSON>',
    age: '35',
    gender: 'Male',
    phone: '9876543210',
    address: '123 Street, City A',
    testNames: [{ name: 'CBC TEST', SLDCode: 'SLDC126' }, { name: 'Urine Test', SLDCode: 'USLD014' }],
    collectionType: 'Draw',
    labName: 'Dr. Path Lab',
    labImage: 'https://picsum.photos/seed/lab1/100/100',
    date: '02-07-2025',
    amount: 1050,
    paymentMethod: 'UPI',
    status: 'order Confirmed'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    age: '29',
    gender: 'Female',
    phone: '9876541230',
    address: '456 Street, City B',
    testNames: [{ name: 'Vitamin D3 Test', SLDCode: 'VSLD020' }],
    collectionType: 'Pickup',
    labName: 'Metro Labs',
    labImage: 'https://picsum.photos/seed/lab2/100/100',
    date: '03-07-2025',
    amount: 890,
    paymentMethod: 'Cash',
    status: 'order Confirmed'
  },
];

const ScheduledCollections = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [data, setData] = useState(mockData);

  const filteredData = data.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderItem = ({ item }) => (
    <View style={styles.card}>
      <View style={styles.headerRow}>
        <Text style={styles.name}>{item.name}</Text>
        <Text style={styles.date}><Icon name="calendar" size={responsiveFontSize(1.5)} /> {item.date}</Text>
      </View>

      <Text style={styles.subText}>{item.phone} | Age: {item.age} | {item.gender}</Text>
      {/* <Text style={styles.subText}>{item.address}</Text> */}

      {/* Tests */}
      <View style={styles.testContainer}>
        <Icon name="flask-outline" size={responsiveFontSize(1.5)} color={Colors.primary} />
        {item.testNames.map((test, i) => (
          <View key={i} style={styles.testChip}>
            <Text style={styles.testText}>{test.name} ({test.SLDCode})</Text>
          </View>
        ))}
      </View>

      {/* Collection and Lab */}
      <View style={styles.detailRow}>
        <View style={styles.labInfo}>
          <Image source={{ uri: item.labImage }} style={styles.labImage} />
          <Text numberOfLines={3} style={[styles.labName, {width: responsiveWidth(58),}]}>{item.labName}</Text>
        </View>
        <Text style={styles.collectionType}>
          <Icon name={item.collectionType === 'Pickup' ? 'truck-check' : 'needle'} size={responsiveFontSize(1.6)} />
          {' '}{item.collectionType}
        </Text>
      </View>

      {/* Amount and Payment */}
      <Text style={styles.amount}>₹{item.amount}</Text>
      <Text style={styles.paymentMethod}>Payment: {item.paymentMethod}</Text>

      {/* Actions */}
      <View style={styles.actionRow}>

        <View style={[styles.testStatusContainer]}>
          <Text style={styles.testStatus}>{item.status}</Text>
        </View>

        <View style={{flexDirection: 'row', alignItems: 'center',}}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.viewButton }]}
            onPress={() => navigation.navigate('ViewScheduledCollectionDetail', { item })}
          >
            <Icon name="eye" size={responsiveFontSize(2)} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.editButton }]}
            onPress={() => console.log('Reschedule')}
          >
            <Icon name="calendar-edit" size={responsiveFontSize(2)} color={Colors.white} />
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.editButton }]}
            onPress={() => navigation.navigate('CreateCollectionLead', { item, isEditMode: true })}
          >
            <Icon name="pencil" size={responsiveFontSize(2.2)} color="#fff" />
          </TouchableOpacity> */}
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.deleteButton }]}
            onPress={() => {
              setData(prev => prev.filter(i => i.id !== item.id));
            }}
          >
            <Icon name="delete" size={responsiveFontSize(2)} color="#fff" />
          </TouchableOpacity>
        </View>

      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Scheduled Collections" onBackPress={() => navigation.goBack()} />
      <View style={styles.container}>
        <Searchbar
          placeholder="Search patient"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.tertiary}
          placeholderTextColor={Colors.tertiary}
        />
        <FlatList
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{ paddingBottom: responsiveHeight(10) }}
          ListEmptyComponent={<Text style={styles.emptyText}>No scheduled collections found</Text>}
        />
      </View>
    </View>
  );
};

export default ScheduledCollections;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
    paddingTop: responsiveHeight(1),
  },
  searchBar: {
    marginBottom: responsiveHeight(1.5),
    borderRadius: responsiveWidth(8),
    elevation: 2,
  },
  searchInput: {
      fontSize: responsiveFontSize(1.6),
      color: Colors.primary,
      fontWeight: '500',
      minHeight: responsiveHeight(4),
    },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(1.5),
    elevation: 3,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  name: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  date: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
  },
  subText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.black,
    marginTop: 2,
  },
  testContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginTop: responsiveHeight(1),
  },
  testChip: {
    backgroundColor: Colors.primaryWithExtraOpacity,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginHorizontal: 4,
    marginTop: 4,
  },
  testText: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.primary,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: responsiveHeight(1),
    alignItems: 'center',
  },
  labInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labImage: {
    width: responsiveWidth(6),
    height: responsiveWidth(6),
    borderRadius: responsiveWidth(4),
    marginRight: responsiveWidth(2),
  },
  labName: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.black,
  },
  collectionType: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.primary,
    fontWeight: 'bold'
  },
  amount: {
    fontSize: responsiveFontSize(1.7),
    fontWeight: 'bold',
    color: Colors.green,
    marginTop: responsiveHeight(0.5),
  },
  paymentMethod: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.tertiary,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: responsiveHeight(1),
  },
  testStatusContainer:{
},
  testStatus:{
    color: Colors.white,
    fontSize: responsiveFontSize(1.5),
    backgroundColor: Colors.success,
    paddingHorizontal: responsiveWidth(2.2),
    paddingVertical: responsiveHeight(0.7),
    borderRadius: responsiveWidth(4),
    textTransform: 'capitalize',
    fontWeight: 'bold',
  },
  actionButton: {
    width: responsiveWidth(8),
    height: responsiveWidth(8),
    borderRadius: responsiveWidth(4.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: responsiveHeight(5),
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
  },
});
