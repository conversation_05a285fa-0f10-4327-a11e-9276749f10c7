import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import CustomButton from '../../components/CustomButton'
import CustomInput from '../../components/CustomInput'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import Colors from '../../styles/Colors'

const Login = ({navigation}) => {
  const currentYear = new Date().getFullYear();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    console.log('Login pressed with username:', username, 'and password:', password);

    try {

    } catch (error) {
      console.error('Error during login:', error);
    } finally {
      setIsLoading(false);
    }


    navigation.navigate('Main');
  }
  const handleForgotPassword = () => {
    // <PERSON><PERSON> forgot password logic here
    console.log('Forgot Password pressed');
    // For example, you can navigate to a forgot password screen
    navigation.navigate('ForgotPassword');
  }

  return (
    <View style={styles.main}>
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
            <Image
              // source={{uri: 'https://verainterior.com/wp-content/uploads/2024/05/Dental-Clinic-Interior-Design-jpg.webp',}}
              source={require('../../assets/Images/2.png')}
              style={styles.logo}
            />
            <Text style={styles.appTitle}>SLD Hospital</Text>
            <Text style={styles.appSubtitle}>Collection Center</Text>
        </View>

        <View style={styles.cardShadow}>
          <View style={styles.card}>
            <Text style={styles.welcomeText}>Welcome Back</Text>
            <Text style={styles.loginText}>Login to your account</Text>
            <View style={styles.inputContainer}>
              <CustomInput title={'UserName'} iconName={'account'} value={username} onChangeText={setUsername} placeholder={'Enter your username'} keyboardType="default" maxLength={50} />
              <CustomInput title={'Password'} iconName={'lastpass'} value={password} onChangeText={setPassword} placeholder={'Enter your password'} keyboardType="default" maxLength={16} eye={true} secureTextEntry={true} />
              <CustomButton title={'Login'} onPress={handleLogin} color={Colors.white} />
            </View>
          </View>
        </View>

      </View>

      <Text style={styles.footerText}>
        © {currentYear} Developed By IT Mingo. All rights reserved.
      </Text>
    </View>
  )
}

export default Login

const styles = StyleSheet.create({
  main: {
    flex:1,
    backgroundColor: Colors.primaryWithExtraOpacity,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(4),
    paddingTop: responsiveHeight(4),
    // paddingBottom: responsiveHeight(2),
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: responsiveHeight(3),
    // backgroundColor:'red',
  },
  logo: {
    width: responsiveWidth(25),
    height: responsiveHeight(12),
    resizeMode: 'cover',
    borderRadius: responsiveWidth(3),
    borderWidth: 2,
    borderColor: Colors.primaryWithOpacity,
  },
  appTitle: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: responsiveHeight(1),
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  appSubtitle: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.green,
    marginTop: responsiveHeight(0.5),
    opacity: 0.9,
  },
  cardShadow: {
    width: '100%',
    borderRadius: responsiveWidth(4),
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    // Elevation for Android
    elevation: 5,
    borderColor: Colors.primaryWithExtraOpacity,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(4),
    padding: responsiveWidth(6),
    width: '100%',
  },
  welcomeText: {
    fontSize: responsiveFontSize(2.5),
    fontWeight: 'bold',
    color: Colors.primary,
    textAlign: 'center',
  },
  loginText: {
    fontSize: responsiveFontSize(1.6),
    // color: '#666',
    color: Colors.green ,
    marginBottom: responsiveHeight(2),
    textAlign: 'center',
  },
  inputContainer: {
    gap: responsiveHeight(2),
  },
  forgotPass:{
    fontSize: responsiveFontSize(1.6),
    color: Colors.green ,
    textAlign: 'right',
  },
  footerText: {
    fontSize: responsiveFontSize(1.4),
    color: '#666',
    marginTop: responsiveHeight(3),
    textAlign: 'center',
  },
})