import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, FlatList, TouchableOpacity } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Searchbar } from 'react-native-paper';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';


const MOCK_TESTS = [
  {
    id: '1',
    testName: 'CBC (Complete Blood Count)',
    labName: 'Apollo Diagnostics',
    price: 250,
    type: 'Draw',
  },
  {
    id: '2',
    testName: 'Vitamin D',
    labName: 'Thyrocare',
    price: 600,
    type: 'Pickup',
  },
  {
    id: '3',
    testName: 'Blood Sugar (Fasting)',
    labName: 'Dr Lal Pathlabs',
    price: 150,
    type: 'Draw',
  },
];

const PriceComparison = ({ navigation }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [filterType, setFilterType] = useState(null);

    const filteredTests = MOCK_TESTS.filter(item =>
        item.testName.toLowerCase().includes(searchQuery.toLowerCase()) &&
        (!filterType || item.type === filterType)
    );

    const handleFilter = (type) => {
        setFilterType(prev => (prev === type ? null : type));
    };

  return (
   <View style={{ flex: 1 }}>
      <MyHeader title="Price Comparison" onBackPress={() => navigation.goBack()} />

      <View style={styles.container}>
        <Searchbar
          placeholder="Search Test"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={Colors.primary}
          inputStyle={styles.searchInput}
        />

        <View style={styles.filterRow}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === 'Draw' && styles.selectedFilter,
            ]}
            onPress={() => handleFilter('Draw')}
          >
            <Icon name="needle" size={20} color={Colors.primary} />
            <Text style={styles.filterText}>Draw</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === 'Pickup' && styles.selectedFilter,
            ]}
            onPress={() => handleFilter('Pickup')}
          >
            <Icon name="truck-check-outline" size={20} color={Colors.primary} />
            <Text style={styles.filterText}>Pickup</Text>
          </TouchableOpacity>
        </View>

        <FlatList
          data={filteredTests}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.card}>
              <View style={styles.cardTop}>
                <Text style={styles.testName}>{item.testName}</Text>
                <Text style={styles.price}>₹ {item.price}</Text>
              </View>
              <Text style={styles.labName}>{item.labName}</Text>
              <Text style={styles.testType}>Type: {item.type}</Text>
            </View>
          )}
          ListEmptyComponent={
            <Text style={styles.noResults}>No matching tests found.</Text>
          }
          contentContainerStyle={{ paddingBottom: responsiveHeight(5) }}
        />
      </View>
    </View>
  )
}

export default PriceComparison


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
    backgroundColor: Colors.white,
  },
  searchBar: {
    marginVertical: responsiveHeight(2),
    borderRadius: responsiveWidth(8),
    backgroundColor: Colors.white,
    elevation: 2,
    borderColor: Colors.primary,
    borderWidth: 1,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: responsiveHeight(1),
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    backgroundColor: Colors.white,
    paddingVertical: responsiveHeight(1),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(5),
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  selectedFilter: {
    backgroundColor: Colors.primaryWithOpacity,
  },
  filterText: {
    color: Colors.primary,
    fontSize: responsiveFontSize(1.6),
  },
  card: {
    borderWidth: 1,
    borderColor: Colors.primaryWithOpacity,
    borderRadius: responsiveWidth(2),
    padding: responsiveWidth(4),
    marginVertical: responsiveHeight(1),
    backgroundColor: Colors.white,
    elevation: 1,
  },
  cardTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: responsiveHeight(0.5),
  },
  testName: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  price: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.success,
  },
  labName: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
  },
  testType: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.secondary,
    marginTop: 2,
  },
  noResults: {
    textAlign: 'center',
    color: Colors.tertiary,
    marginTop: responsiveHeight(5),
    fontSize: responsiveFontSize(1.6),
  },
});