import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, ToastAndroid } from 'react-native'
import React, { useEffect, useState } from 'react'
import Colors from '../../styles/Colors'
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import ImageWithFallback from '../../components/ImageWithFallback';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import SettingComponent from '../../components/SettingComponent';
import LinearGradient from 'react-native-linear-gradient';
import CustomButton from '../../components/CustomButton';
import ConfirmationDialog from '../../components/ConfirmationDialog';
import BaseURL from '../../components/BaseURL';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import useFetchProfile from '../../components/useFetchProfile';

// Sample profile data
const PROFILE_DATA = {
  id: '1',
  title: 'title',
  name: '<PERSON>',
  age: '26',
  sex: 'Female',
  bg: 'A+',
  email: '<EMAIL>',
  phone: '+91 9876543210',
  
  collectionCenterName: 'Sld Center Name',
  address: '123 Main Street, Raipur,',
  district: 'Raipur',
  state: 'Chhattishgarh',
  image: 'https://randomuser.me/api/portraits/women/2.jpg',
};

const Profile = ({navigation}) => {
  const [userProfile, setUserProfile] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  const { data, profileLoading } = useFetchProfile()

  // Handle logout button press
  const handleLogout = () => {
    setLogoutDialogVisible(true);
  };

  const performLogout = async () => {
    try{
      setIsLoading(true);
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        showToast('Session expired, please login again');
        await AsyncStorage.clear();
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login2' }]
        });
        return;
      }

      const baseUrl = await BaseURL.uri;
      const response = await fetch(`${baseUrl}/collection-centers/logout`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const result = await response.json();
      console.log('Logout response:', result);
      if (response.ok) {
        // Clear the token from AsyncStorage
        await AsyncStorage.removeItem('authToken');
        showToast('Logged out successfully');
        setLogoutDialogVisible(false);
        await AsyncStorage.clear();
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login2' }],
        });
      } else {
        console.error('Failed to logout:', result);
        showToast('Something went wrong')
      }

    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const showToast = (msg) => {
      ToastAndroid.showWithGravity(msg, ToastAndroid.SHORT, ToastAndroid.CENTER);
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <LinearGradient colors={[Colors.primaryWithOpacity, '#ffffff']} 
          start={{x: 0, y: 0}}
          end={{x: 0, y: 1}} 
        >
        <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
                <ImageWithFallback source={{ uri: PROFILE_DATA.image }} style={styles.profileImage} iconName="account" iconSize={8} />
                <Text style={styles.profileName}>{data?.center_name}</Text>
                <View style={styles.profileInfo}>
                  <Icon name="account-check" size={responsiveFontSize(3)} color={Colors.primary} />
                  <Text style={styles.profileSpecialization}>{data?.mobile_no || data?.alternate_mobile_no}</Text>
                </View>
                {/* <TouchableOpacity
                  style={styles.editProfileButton}
                  // onPress={() => navigation.navigate('ProfileEdit', { doctorProfile: DOCTOR_PROFILE })}
                >
                  <Icon name="pencil" size={responsiveFontSize(2)} color={Colors.white} />
                </TouchableOpacity> */}
            </View>            
        </View>
        </LinearGradient>

        <View style={{paddingHorizontal: responsiveWidth(4)}}> 
            {/* Management Section */}
            <View style={styles.settingsSection}>
              <Text style={styles.sectionTitle}>Management</Text>
                <SettingComponent
                  icon="account-group"
                  title="Profile Management"
                  subtitle="Manage collection center"
                  showToggle={false}
                  onPress={() => navigation.navigate('ProfileManagement')}
                  color={Colors.primary}
                />
                <SettingComponent
                  icon="account-tie-outline"
                  title="Phlebo / Pickup Boy"
                  subtitle="View assigned staff details"
                  showToggle={false}
                  onPress={() => navigation.navigate('PhleboBoyDetails')}
                  color={Colors.warning}
                />
                <SettingComponent
                  icon="key"
                  title="Change Password"
                  showToggle={false}
                  onPress={() => navigation.navigate('ChangePassword')}
                  color={Colors.error}
                />
            </View>

            {/* Billing Section */}
            <View style={styles.settingsSection}>
              <Text style={styles.sectionTitle}>Billing</Text>
              <SettingComponent
                icon="history"
                title="Billing Summary"
                subtitle="View billing details"
                showToggle={false}
                onPress={() => navigation.navigate('BillingSummary')}
                color={Colors.info}
              />
              <SettingComponent
                icon="history"
                title="Payment & Balance / History"
                subtitle="View balance and past transactions"
                showToggle={false}
                onPress={() => navigation.navigate('PaymentHistory')}
                color={Colors.info}
              />
            </View>   

            {/* SLD Store Section */}
            <View style={styles.settingsSection}>
              <Text style={styles.sectionTitle}>Store</Text>
                <SettingComponent
                  icon="store"
                  title="SLD Store"
                  subtitle="Browse lab items & supplies"
                  showToggle={false}
                  onPress={() => navigation.navigate('SLDStore')}
                  color={Colors.pink}
                />
            </View>

            {/* App Info Section */}
            <View style={styles.settingsSection}>
              <Text style={styles.sectionTitle}>App</Text>
                <SettingComponent
                  icon="share"
                  title="Refer App"
                  showToggle={false}
                  onPress={() => navigation.navigate('ReferApp')}
                  color={Colors.info}
                />
                <SettingComponent
                  icon="gift-outline"
                  title="Rewards"
                  showToggle={false}
                  onPress={() => navigation.navigate('Rewards')}
                  color={Colors.pink}
                />
                <SettingComponent
                  icon="shield-check-outline"
                  title="Privacy Policy"
                  showToggle={false}
                  onPress={() =>  navigation.navigate('PrivacyPolicy')}
                  color={Colors.info}
                />
                <SettingComponent
                  icon="comment-alert-outline"
                  title="FAQ"
                  showToggle={false}
                  onPress={() => console.log('FAQ')}
                  color={Colors.warning}
                />
                <SettingComponent
                  icon="help-circle-outline"
                  title="Feedback & Suggestions"
                  showToggle={false}
                  onPress={() => navigation.navigate('FeedbackSuggestions')}
                  color={Colors.success}
                />
            </View>
        </View>
        <View style={{paddingHorizontal: responsiveWidth(22), marginBottom: responsiveHeight(3)}}>
          <CustomButton title="Logout" color={Colors.error} bgColor={Colors.white} borderC={Colors.error} onPress={handleLogout} />
        </View>
      </ScrollView>

      {/* Logout Confirmation Dialog */}
      <ConfirmationDialog
        visible={logoutDialogVisible}
        onClose={() => setLogoutDialogVisible(false)}
        title="Logout"
        message="Are you sure you want to logout?"
        confirmText="Logout"
        cancelText="Cancel"
        onConfirm={performLogout}
        icon="logout"
        iconColor={Colors.error}
        isLoading={isLoading}
      />
    </View>
  )
}

export default Profile

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollView: {
    flex: 1,
    // paddingHorizontal: responsiveWidth(4),
  },
  profileCard: {
    // marginVertical: responsiveHeight(1.5),
    // padding: responsiveWidth(4),
    // backgroundColor: Colors.green,
    // borderRadius: responsiveWidth(3),
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // elevation: 3,
    marginTop: responsiveHeight(4),
  },
  profileHeader: {
    // flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
    gap: responsiveHeight(2),
  },
  profileImage: {
    // width: responsiveWidth(20),
    // height: responsiveWidth(20),
    // borderRadius: responsiveWidth(10),
    // borderWidth: 2,
    // borderColor: Colors.primaryWithExtraOpacity,
    width: responsiveWidth(25),
    height: responsiveHeight(12),
    resizeMode: 'cover',
    borderRadius: responsiveWidth(3),
    borderWidth: 2,
    borderColor: Colors.primaryWithOpacity,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: responsiveWidth(1),
    backgroundColor: `${Colors.primary}20`,
    paddingHorizontal: responsiveWidth(2.8),
    paddingVertical: responsiveHeight(1),
    borderRadius: responsiveWidth(6),

  },
  profileName: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.black,
  },
  profileSpecialization: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.primary,
    fontWeight: '700'
  },
  profileExperience: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: responsiveHeight(0.5),
  },
  profileExperienceText: {
    fontSize: responsiveFontSize(1.4),
    color: Colors.tertiary,
    marginLeft: responsiveWidth(1),
  },
  editProfileButton: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(5),
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileDetails: {
    borderTopWidth: 1,
    borderTopColor: Colors.primaryWithExtraOpacity,
    // paddingTop: responsiveHeight(1.5),
  },
  profileDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: responsiveHeight(1),
  },
  profileDetailText: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.blackColor,
    marginLeft: responsiveWidth(2),
    flex: 1,
  },
  settingsSection: {
    backgroundColor: Colors.white,
    // borderTopLeftRadius: responsiveWidth(8),
    // borderTopRightRadius: responsiveWidth(8),
    marginBottom: responsiveHeight(2),
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(4),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(1.5),
  },
})