import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
  ToastAndroid,
  Text,
  ActivityIndicator,
  PermissionsAndroid,
} from 'react-native';
import { responsiveHeight, responsiveWidth, responsiveFontSize } from 'react-native-responsive-dimensions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6';
import PickImageComponent from '../../../components/PickImageComponent';
import Colors from '../../../styles/Colors';
import CustomButton from '../../../components/CustomButton';
import CustomDropDown from '../../../components/CustomDropDown';
import CustomInput2 from '../../../components/CustomInput2';
import MyHeader from '../../../components/MyHeader';
import ImageWithFallback from '../../../components/ImageWithFallback';
import CustomDateTimePicker from '../../../components/CustomDateTimePicker';
import Geolocation from '@react-native-community/geolocation';
import useFetchState from '../../../components/useFetchState';
import useFetchBloodGroup from '../../../components/useFetchBloodGroup';
import BaseURL from '../../../components/BaseURL';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useFetchProfile from '../../../components/useFetchProfile';

 const sexOptions = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
    { label: 'Transgender', value: 'Transgender' },
  ];

const ProfileManagement = ({ navigation }) => {
  const [title, setTitle] = useState('');
  const [name, setName] = useState('');
  const [doBirth, setDoBirth] = useState(null);
  const [sex, setSex] = useState('');
  const [bloodGroup, setBloodGroup] = useState('');
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [centerName, setCenterName] = useState('');
  const [location, setLocation] = useState(null);
  const [address, setAddress] = useState('');
  const [state, setState] = useState('');
  const [city, setCity] = useState('');
  const [pinCode, setPinCode] = useState('');
  const [profileImage, setProfileImage] = useState(null);
  const [loading, setLoading] = useState(false);

  const { stateList } = useFetchState();
  const { bloodGroupList } = useFetchBloodGroup();
  const [cityList, setCityList] = useState([]);
  const { data, profileLoading, refetch } = useFetchProfile()

  
  const handleChange = (key, value) => {
    setForm(prev => ({ ...prev, [key]: value }));
  };

  const handlePickImage = async () => {
    const image = await PickImageComponent();
    if (image) {
      console.log("Selected image object:", image);

      try{
        const token = await AsyncStorage.getItem('authToken');
        if (!token) {
          showToast('Session expired, please login again');
          await AsyncStorage.clear();
          navigation.reset({
            index: 0,
            routes: [{ name: 'Login2' }]
          });
          return;
        }
          const formData = new FormData();
          formData.append('profile', {
            uri: image.uri,
            type: image.type || 'image/jpeg',
            name: image.fileName || 'profile.jpg',
          });

          const baseUrl = await BaseURL.uri;
          const response = await fetch(`${baseUrl}/collection-centers/profile-image-update`, {
            method: 'POST',
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${token}`,
            },
            body: formData,
          });

          const result = await response.json();

          if (response.ok) {
            console.log('Image upload success:', result);
            showToast('Profile image updated successfully');
            await refetch();
          } else {
            console.log('Image upload failed:', response.status);
            showToast('Failed to upload profile image');
          }
        
      } catch (error) {
        console.error("Error picking image:", error);
        showToast('Failed to pick image');
      }
    }
  };

  const handleSubmit = () => {
    const form = {
      title,
      name,
      doBirth,
      sex,
      bloodGroup,
      email,
      mobile,
      centerName,
      location,
      address,
      city,
      state,
      pinCode,
      profileImage,
    }

    console.log('Form Data:', form);
    console.log('Profile Image:', profileImage);
    // TODO: Submit data to server
  };

  const getCityList = async () => {
    try {
      // setLoading(true)
      const baseUrl = BaseURL.uri
      const response = await fetch(`${baseUrl}/cities?state_id=${state}`)
      const result = await response.json()
      if (response.ok) {
        console.log(result)
        const formattedCitys = result.data.map(states => ({
          label: states.name,
          value: states.id.toString()
        }))
        setCityList(formattedCitys)
      } else {
        console.log('else', result)
      }
    } catch (e) {
      console.log('catch', e)
    } finally {
      // setLoading(false)
    }
  }

  useEffect(() => {
    if (state) {
      getCityList()
    }
  }, [state])


  const requestLocationPermission = async () => {
    console.log('Requesting location permission...');
    try {
      // Ask both fine and coarse location
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
      ]);

      const fine = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;
      const coarse = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED;

      if (fine || coarse) {
        console.log("permission granted")
        getCurrentPosition();
        
      } else {
        showToast('Please enable location permission in settings.');
        setLoading(false);
      }
    } catch (err) {
      console.warn(err);
      showToast('Permission error');
    }
  }

  const getCurrentPosition = () => {
    console.log('Fetching current location...');
    setLoading(true);

    Geolocation.getCurrentPosition(
      (position) => {
        console.log('Location:', position.coords);
        setLocation(position.coords);
        setLoading(false);
      },
      (error) => {
        console.log('GetCurrentPosition Error:', error);
        console.log(`❌ GetCurrentPosition Error [code ${error.code}]:`, error.message);

        if (error.code === 1) {
          showToast('Permission denied.');
        } else if (error.code === 2) {
          showToast('Location unavailable. Turn on GPS.');
        } else if (error.code === 3) {
          // showToast('Location timeout. Try again.');
        } else {
          // showToast(error.message || 'Unknown location error');
        }
        useWatchPositionFallback()
        // setLoading(false);
      },
      {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  };

  // fallback using watchPosition
  const useWatchPositionFallback = () => {
    const watchId = Geolocation.watchPosition(
      (position) => {
        console.log('✅ Fallback position:', position.coords);
        setLocation(position.coords);
        Geolocation.clearWatch(watchId);
        // navigation.reset({ index: 0, routes: [{ name: 'Dashboard' }] })
        setLoading(false);
      },
      (error) => {
        console.log('❌ Fallback error:', error);
        showToast('Unable to fetch location');
        setLoading(false);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 0,
        interval: 5000,
        fastestInterval: 2000,
      }
    );
  };

  useEffect(() => {
    requestLocationPermission()
  }, [])

  const showToast = (msg) => {
    ToastAndroid.showWithGravity(msg, ToastAndroid.LONG, ToastAndroid.CENTER);
  };

  return (
    <View style={styles.container}>
      <MyHeader title="Profile Management" onBackPress={() => navigation.goBack()} />
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : null} style={{ flex: 1 }}>
        <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>

          {/* Profile Image Picker */}
          <TouchableOpacity onPress={handlePickImage} style={styles.imageWrapper}>
            <ImageWithFallback source={data?.profile_image_url ? { uri: data.profile_image_url } : null} style={styles.profileImage} iconName="account" iconSize={7} />
            <View style={styles.imageOverlay}>
              <MaterialCommunityIcons name="camera" color={Colors.white} size={responsiveFontSize(2.5)} />
            </View>
          </TouchableOpacity>

          {/* Form Inputs */}
          <CustomInput2 label="Title" icon="account-tie" value={title} onChangeText={setTitle} />
          <CustomInput2 label="Name" icon="account" value={name} onChangeText={setName} />
          <CustomDateTimePicker placeholder="Date of Birth" value={doBirth} setDate={setDoBirth} iconName="calendar" maximumDate={new Date()} />

          <View style={styles.fieldCmn}>
            <View style={styles.fieldLeft}>
                <CustomDropDown iconName="gender-male-female" value={sex} setValue={setSex} data={sexOptions} placeholder="Gender" uprLabel="Gender" />
            </View>
            <View style={styles.fieldRight}>
                <CustomDropDown iconName="water" value={bloodGroup} setValue={setBloodGroup} data={bloodGroupList} placeholder="Blood Group" uprLabel="Blood Group" />
            </View>
          </View>

          <CustomInput2 label="Collection Center/Clinic Name" icon="hospital-building" value={centerName} onChangeText={setCenterName} />
          <CustomInput2 label="Email" icon="email" keyboardType="email-address" value={email} onChangeText={setEmail} />
          <CustomInput2 label="Mobile" icon="phone" keyboardType="phone-pad" value={mobile} onChangeText={setMobile} />
          <View style={styles.fieldCmn}>
             <View style={styles.fieldLeft}>
              <CustomInput2 label="Center Location" icon="map-marker" keyboardType={'number-pad'} editable={false} value={location ? `${location.latitude}, ${location.longitude}` : ''} onChangeText={(text) => console.log(text)} />
            </View>
            {loading ? (
                <View style={{ flex: 1, }}>
                  <ActivityIndicator size={responsiveFontSize(2.2)} color={Colors.primary} />
                </View>
              ) : 
                <TouchableOpacity onPress={()=>requestLocationPermission()} style={{flex:1 ,flexDirection:'row', alignItems:'center', gap:responsiveWidth(2)}}>
                  <FontAwesome6 name="location-crosshairs" size={responsiveFontSize(2.2)} color={Colors.primary} />
                  <Text style={{fontSize:responsiveFontSize(1.6), color:Colors.primary}}>Get Location</Text>
                </TouchableOpacity>
            }
          </View>
          <CustomInput2 label="Address" icon="home" value={address} onChangeText={setAddress} />
          <CustomInput2 label="Pincode" icon="map-marker-radius" keyboardType="number-pad" value={pinCode} onChangeText={setPinCode} />
          <View style={styles.fieldCmn}>
            <View style={[styles.fieldLeft,{flex:2.1}]}>
              <CustomDropDown iconName="flag" value={state} setValue={setState} data={stateList} placeholder="State" uprLabel={'State'} />
            </View>
            <View style={[styles.fieldRight,{flex:2.1}]}>
              <CustomDropDown iconName="city" value={city} setValue={setCity} data={cityList} placeholder="City" uprLabel={'City'} dropdownPosition='top'  />
            </View>
          </View>

          <CustomButton title="Save Profile" onPress={handleSubmit} color={Colors.white}  />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}

export default ProfileManagement


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollContainer: {
    // flexGrow:1,
    padding: responsiveWidth(5),
    paddingBottom: responsiveHeight(4),
  },
  imageWrapper: {
    alignSelf: 'center',
    marginBottom: responsiveHeight(2),
    // position: 'relative',
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    backgroundColor: Colors.lightGray
  },
  profileImage: {
    width: responsiveWidth(30),
    height: responsiveWidth(30),
    borderRadius: responsiveWidth(15),
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    padding: 6,
    borderWidth: 2,
    borderColor: Colors.white,
  },

   fieldCmn:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: responsiveWidth(2),
    marginTop: responsiveHeight(0.5)
  },
  fieldLeft:{
    flex:2,
  },
  fieldRight:{
    flex:2,
  },
});