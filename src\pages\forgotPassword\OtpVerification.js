import React, { useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, Keyboard } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import MyHeader from '../../components/MyHeader';
import OtpBox from '../../components/OtpBox';
import CustomButton from '../../components/CustomButton';
import BottomLink from '../../components/BottomLink';
import Colors from '../../styles/Colors';

const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m}:${s < 10 ? '0' : ''}${s}`;
};

const OtpVerification = ({ navigation, route }) => {
    const { phoneNumber } = route.params;
    const [counter, setCounter] = useState(90);

    useEffect(() => {
        let timer = null;
        if (counter > 0) {
        timer = setTimeout(() => setCounter(counter - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [counter]);

    const handleVerify = () => {
        console.log('Verifying OTP');
        // Add actual verification logic here
        navigation.reset({
        index: 0,
        routes: [{name: 'Login2'}],
        })
    };

    const handleResend = () => {
        console.log('resend otp');
        setCounter(90); // Restart timer
    };

  return (
    <View style={styles.main}>
      <MyHeader title="Verification" onBackPress={() => navigation.goBack()} />
      <View style={styles.container}>
          <OtpBox number={phoneNumber} title={'OTP Verification'} />

          <CustomButton title="Verify OTP" onPress={handleVerify} color={Colors.white} />

          <View style={{ marginTop: responsiveHeight(2), alignSelf: 'center', }}>
            {counter > 0 ? (
                <Text style={styles.resendText}>Resend in {formatTime(counter)}</Text>
            ) : (
                <BottomLink text="Didn't receive the code? " subText="Resend" subTextColor={Colors.primary} onPress={handleResend} />
            )}
          </View>
      </View>
    </View>
  )
}

export default OtpVerification



const styles = StyleSheet.create({
  main:{
    flex:1,
    backgroundColor: Colors.white,
  },
  container: {
    // flex: 1,
    paddingHorizontal: responsiveWidth(8),
    justifyContent: 'center',
  },
  title: {
    fontSize: responsiveFontSize(3),
    fontWeight: 'bold',
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: responsiveHeight(1),
  },
  subtitle: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.green,
    textAlign: 'center',
    marginBottom: responsiveHeight(4),
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: responsiveHeight(4),
  },
  otpInput: {
    width: responsiveWidth(12),
    height: responsiveHeight(6),
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: responsiveWidth(2),
    textAlign: 'center',
    fontSize: responsiveFontSize(2.5),
    color: Colors.primary,
    backgroundColor: '#fff',
    elevation: 2,
  },
  resendText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
    textAlign: 'center',
    marginTop: responsiveHeight(2),
  },
  resendLink: {
    color: Colors.primary,
    fontWeight: '600',
  },
});