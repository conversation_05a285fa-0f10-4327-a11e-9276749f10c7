import React, { useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { ToastAndroid } from 'react-native';
import BaseURL from './BaseURL';

const useFetchBloodGroup = () => {
    const [bloodGroupList, setBloodGroupList] = useState([]);
    const [loading, setLoading] = useState(true);

    const showToast = (msg) => {
        ToastAndroid.showWithGravity(msg, ToastAndroid.TOP, ToastAndroid.SHORT)
    }

    const getBloodGroup = async () => {
        try {
            setLoading(true)

            const baseUrl = await BaseURL.uri;
            const response = await fetch(`${baseUrl}/blood-groups`, {
                method: 'GET',
            });

            const result = await response.json();
            if (response.ok) {
                console.log(result)
                const formattedBloodGrp = result.data.map(states => ({
                    label: states.name,
                    value: states.id.toString()
                }))
                setBloodGroupList(formattedBloodGrp)
            } else {
                showToast('Something went wrong');
                console.log('Response error:', result);
            }
        } catch (e) {
            // showToast('Something went wrong');
            console.log('Fetch error:', e);
        } finally {
            setLoading(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            getBloodGroup()
        }, [])
    )

    return { bloodGroupList, loading, refetch: getBloodGroup };
};

export default useFetchBloodGroup;