import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import { Searchbar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import MyHeader from '../../components/MyHeader';
import ImageWithFallback from '../../components/ImageWithFallback';
import Colors from '../../styles/Colors';

const mockCollections = [
  {
    id: '1',
    name: '<PERSON>',
    age: '32',
    gender: 'Male',
    phone: '9876543210',
    address: '123 Main St, City A',
    testNames: [{ name: 'CBC TEST', SLDCode: 'SLDC126' }, { name: 'Urine Test', SLDCode: 'USLD014' }],
    date: '02-07-2025',
    collectionType: 'Draw',
    // amount: '500',
    // labName: 'Lab A',
    // paymentMode: 'UPI',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    age: '29',
    gender: 'Female',
    phone: '9876541230',
    address: '456 Street, City B',
    testNames: [{ name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020' }, { name: 'THYROID PROFILE TEST', SLDCode: 'TSLD046' },{ name: 'THYROID PROFILE TEST', SLDCode: 'TSLD046' },{ name: 'THYROID PROFILE TEST', SLDCode: 'TSLD046' }],
    date: '03-07-2025',
    collectionType: 'Pickup',
    // amount: '750',
    // labName: 'Lab B',
    // paymentMode: 'Cash',
  },
  {
    id: '3',
    name: 'Neha Sharma',
    age: '25',
    gender: 'Female',
    phone: '9876541210',
    address: '456 Street, City C',
    testNames: [{ name: 'VITAMIN D3 TEST', SLDCode: 'VSLD020' }, { name: 'THYROID PROFILE TEST', SLDCode: 'TSLD046' },],
    date: '28-07-2025',
    collectionType: 'Draw & Pickup',
    // amount: '750',
    // labName: 'Lab B',
    // paymentMode: 'Cash',
  },
];

const Collections = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [collections, setCollections] = useState(mockCollections);
  const [expandedId, setExpandedId] = useState(null);

  const filteredCollections = collections.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDelete = (id) => {
    setCollections(prev => prev.filter(item => item.id !== id));
  };

  const renderItem = ({ item }) => (
    <View style={styles.card}>
      <View style={styles.patientInfo}>
         <ImageWithFallback
          source={{ uri: item?.image }}
          style={styles.patientImage}
          iconName="account"
          iconSize={4}
        />
        <View style={{flex: 1}}>
            {/* <Text style={[styles.name,{color:Colors.primary}]}>#{item.id}</Text> */}
            <View style={styles.cardHeader}>
              <Text style={[styles.name,{color:Colors.tertiary}]}>#{item.id}</Text>
              <Text style={styles.date}>
                <Icon name="calendar" size={responsiveFontSize(1.6)} color={Colors.tertiary} /> {item.date}
              </Text>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: responsiveHeight(0.5)}}>
              <Text numberOfLines={1} style={[styles.name,{width: responsiveWidth(36),}]}>{item.name}</Text>
              <View style={styles.collectionContainer}>
                  <Icon name={item.collectionType === 'Pickup' ? 'truck-check' : 'needle'} size={responsiveFontSize(1.8)} color={Colors.primary} />
                  {/* {' '}{item.collectionType} */}
                  <Text style={styles.collectionnType}>{item.collectionType}</Text>
              </View>
            </View>
            <Text style={[styles.info,{color: Colors.tertiary}]}> {item.phone}</Text>

            <View style={styles.cardRow}>
                <View style={[styles.genderBadge, { backgroundColor: item.gender === 'Male' ? Colors.card3 : Colors.card4 }]}>
                  <Icon
                    name={item.gender === 'Male' ? 'gender-male' : 'gender-female'}
                    size={responsiveFontSize(1.4)}
                    color={Colors.primary}
                  />
                  <Text style={styles.genderText}>{item.gender}</Text>
                </View>
                <View style={styles.cardRowCenter}>
                  <Icon name="calendar-clock" size={responsiveFontSize(1.4)} color={Colors.tertiary} />
                  <Text style={styles.infoAge}>Age: {item.age}</Text>
                </View>
            </View>
        </View>
      </View>

      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Icon name="map-marker" size={responsiveFontSize(1.6)} color={Colors.tertiary} />
          <Text style={[styles.info, {color: Colors.tertiary}]}>Address: {item.address}</Text>
      </View>

      {/* Tests */}
      <View style={[styles.cardRowCenter, { flexWrap: 'wrap', marginTop: responsiveHeight(0.5)}]}>
        <Icon name="flask-outline" size={responsiveFontSize(1.5)} color={Colors.primary} />
        <View style={styles.testContainer}>
          {(item.testNames || []).map((test, index) => (
            <View key={index} style={styles.testTag}>
              <Text style={styles.testTagText}>{test.name} ({test.SLDCode})</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.genderBadge, { backgroundColor: Colors.addButton }]}
          onPress={() => navigation.navigate('ChooseLab', { item })}
        >
          <Icon name="calendar-plus" size={responsiveFontSize(1.6)} color={Colors.white} />
          <Text style={styles.buttonText}>Compare & Choose Lab</Text>
        </TouchableOpacity>
        <View style={styles.cardHeader}>
            {/* <TouchableOpacity
              style={[styles.actionButton, {backgroundColor: Colors.viewButton,}]}
              onPress={() => navigation.navigate('ViewCollectionDetails', { item })}
            >
              <Icon name="eye" size={responsiveFontSize(1.8)} color={Colors.white} />
              
            </TouchableOpacity> */}

            <TouchableOpacity
              style={[styles.actionButton, {backgroundColor: Colors.editButton,}]}
              onPress={() => navigation.navigate('CreateCollectionLead', { item, isEditMode : true })}
            >
              <Icon name="pencil" size={responsiveFontSize(1.8)} color={Colors.white} />
              {/* <Text style={styles.buttonText}>Edit</Text> */}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: Colors.deleteButton }]}
              onPress={() => handleDelete(item.id)}
            >
              <Icon name="delete" size={responsiveFontSize(1.8)} color={Colors.white} />
              {/* <Text style={styles.buttonText}>Delete</Text> */}
            </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader
        title="Collection List"
        onBackPress={() => navigation.goBack()}
        onFabPress={() => navigation.navigate('CreateCollectionLead')}
        fabTitle={'Create Collection / Lead'}
      />
      <View style={styles.container}>
        <Searchbar
          placeholder="Search by patient name"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.tertiary}
          placeholderTextColor={Colors.tertiary}
        />
        <FlatList
          data={filteredCollections}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{ paddingBottom: responsiveHeight(10) }}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No collections found</Text>
          }
        />
      </View>
    </View>
  );
};

export default Collections;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: responsiveWidth(4),
  },
  searchBar: {
    marginVertical: responsiveHeight(1.5),
    borderRadius: responsiveWidth(8),
    elevation: 2,
    // backgroundColor: Colors.white,
    // borderColor: Colors.primary,
    // borderWidth: 1,
  },
  searchInput: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.primary,
    fontWeight: '500',
    minHeight: responsiveHeight(4),
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(2),
    marginBottom: responsiveHeight(1.5),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  name: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
    color: Colors.primary,
  },
  collectionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      backgroundColor: Colors.white,
      paddingVertical: responsiveHeight(0.5),
      paddingHorizontal: responsiveWidth(2),
      borderRadius: responsiveWidth(5),
      borderWidth: 1,
      borderColor: Colors.primary,
    },
   collectionnType: {
      fontSize: responsiveFontSize(1.4),
      color: Colors.primary,
      fontWeight: 'bold'
    },
  date: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.tertiary,
  },
  info: {
    fontSize: responsiveFontSize(1.5),
    color: Colors.black,
    marginTop: responsiveHeight(0.3),
  },
  amount: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.green,
    fontWeight: '600',
    marginTop: responsiveHeight(0.8),
  },
  emptyText: {
    textAlign: 'center',
    marginTop: responsiveHeight(5),
    color: Colors.tertiary,
    fontSize: responsiveFontSize(1.8),
  },
  testContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: responsiveWidth(1.5),
  },
  testTag: {
    backgroundColor: Colors.primaryWithExtraOpacity,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 15,
    marginRight: 5,
    marginTop: 5,
  },
  testTagText: {
    fontSize: responsiveFontSize(1.3),
    color: Colors.primary,
    fontWeight: '500',
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: responsiveHeight(0.5),
  },
  cardRowCenter:{
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoAge:{
    fontSize: responsiveFontSize(1.2),
    color: Colors.tertiary,
    marginLeft: responsiveWidth(1),
  },
  genderBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(1.8),
    paddingVertical: responsiveHeight(0.3),
    borderRadius: responsiveWidth(6),
    marginRight: responsiveWidth(2),
  },
  genderText: {
    fontSize: responsiveFontSize(1.2),
    color: Colors.primary,
    marginLeft: responsiveWidth(1),
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: responsiveHeight(1),
    borderTopWidth: 1,
    borderTopColor: Colors.primaryWithExtraOpacity,
    paddingTop: responsiveHeight(0.5),
  },
  actionButton: {
    width: responsiveWidth(8),
    height: responsiveWidth(8),
    borderRadius: responsiveWidth(4),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: responsiveWidth(2),
  },
  buttonText: {
    color: Colors.white,
    marginLeft: responsiveWidth(0.6),
    fontSize: responsiveFontSize(1.4),
    fontWeight: '700',
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
   patientImage: {
    width: responsiveWidth(12),
    height: responsiveWidth(12),
    borderRadius: responsiveWidth(7),
    marginRight: responsiveWidth(3),
    borderWidth: 2,
    borderColor: Colors.primaryWithExtraOpacity,
    resizeMode:'stretch',
  },
});
