import {  StatusBar, StyleSheet, useColorScheme, View } from 'react-native';
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import StackNavigation from './src/navigation/StackNavigation';

const App = () => {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <SafeAreaView style={{flex:1}}>
          <StackNavigation />
      </SafeAreaView>
    </View>
  )
}

export default App

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
