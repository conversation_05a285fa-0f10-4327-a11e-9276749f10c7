import { ToastAndroid } from 'react-native'
import React, { useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage';
import BaseURL from './BaseURL';
import { useFocusEffect, useNavigation } from '@react-navigation/native';

const useFetchProfile = () => {
    const navigation = useNavigation();
    const [data, setData] = useState(null);
    const [profileLoading, setLoading] = useState(true);

    const showToast = (msg) => {
        ToastAndroid.showWithGravity(msg, ToastAndroid.TOP, ToastAndroid.SHORT)
    }

    const getProfile = async () => {
        try {
            setLoading(true)
            const token = await AsyncStorage.getItem('authToken');
            if (!token) {
                showToast('Session expired, please login again');
                await AsyncStorage.clear();
                navigation.reset({
                    index: 0,
                    routes: [{ name: 'Login2' }]
                });
                return;
            }

            const baseUrl = await BaseURL.uri;
            const response = await fetch(`${baseUrl}/collection-centers/profile`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (response.ok) {
                setData(result.data);
                console.log(result)
            } else {
                showToast('Something went wrong');
                console.log('Response error:', result);
            }
        } catch (e) {
            // showToast('Something went wrong');
            console.log('Fetch error:', e);
        } finally {
            setLoading(false);
        }
    };

     useFocusEffect(
        React.useCallback(() => {
            getProfile()
        }, [])
    )
    
  return { data, profileLoading, refetch: getProfile };
};

export default useFetchProfile;