import React from 'react';
import { View, Text, StyleSheet, FlatList, Image, Platform, ToastAndroid, TouchableOpacity } from 'react-native';
import { responsiveFontSize, responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions';
import Colors from '../../styles/Colors';
import MyHeader from '../../components/MyHeader';
import Clipboard from '@react-native-clipboard/clipboard';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const OffersPage = ({ navigation }) => {
  const BANNER_HEIGHT = responsiveHeight(3.6);

    const offers = [
    {
      id: '1',
      title: '25% OFF on Full Body Checkup',
      description: 'Save big on your health tests.',
      code: 'HEALTH25',
      validTill: '31st July 2025',
      status: 'Active',
    },
    {
      id: '2',
      title: 'Flat ₹200 OFF on Lab Tests',
      description: 'Use coupon for instant discount.',
      code: 'LAB200',
      validTill: '15th August 2025',
      status: 'Limited Time',
    },
    {
      id: '3',
      title: 'Free Consultation',
      description: 'On booking above ₹999.',
      code: 'FREECONSULT',
      validTill: 'Expired',
      status: 'Expired',
    },
  ];

    const renderItem = ({ item }) => (
        <View style={[styles.card, {backgroundColor: item.status === 'Expired' ? Colors.lightGray : Colors.white, paddingTop: BANNER_HEIGHT + responsiveHeight(1), opacity: item.status === 'Expired' ? 0.6 : 1,}]}>
           {/* ── Banner shown only when expired ── */}
          {item.status === 'Expired' && (
            <View style={[styles.expiredOverlay,]}>
              <Text style={styles.expiredText}>Offer Not Available</Text>
            </View>
          )}

          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: responsiveHeight(0.5) }}>
            <Icon name="tag" size={responsiveFontSize(2)} color={Colors.primary} />
            <Text style={[styles.offerTitle, { marginLeft: responsiveWidth(2)}, item.status === 'Expired' && styles.strikeThroughText,]}>{item.title}</Text>
          </View>
          <Text style={styles.description}>{item.description}</Text>
          <View style={styles.row}>
              <Text style={styles.label}>Code:</Text>
              <Text style={[styles.code, item.status === 'Expired' && styles.strikeThroughText,]}>{item.code}</Text>
              {item?.status != 'Expired' && 
                <TouchableOpacity onPress={() => { 
                    Clipboard.setString(item.code)
                    Platform.OS === 'android' && ToastAndroid.show('Phone number copied', ToastAndroid.SHORT); 
                    }}
                >
                  <Icon name="content-copy" size={responsiveFontSize(2)} color={Colors.primary} style={{ marginLeft: responsiveWidth(2)}}  />
                </TouchableOpacity>
              }
          </View>
          <View style={styles.row}>
              <Text style={styles.label}>Valid Till:</Text>
              <Text style={[styles.value,]}>{item.validTill}</Text>
          </View>
          {item?.status != 'Expired' && 
            <View style={styles.statusTag(item.status)}>
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
          }
        </View>
    );
  
  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Offers" onBackPress={() => navigation.goBack()} />
      <FlatList
        data={offers}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ padding: responsiveWidth(4), flexGrow: 1, }}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Icon name="sale" size={responsiveFontSize(8)} color={Colors.primary} style={{ marginBottom: responsiveHeight(1) }} />
            <Text style={styles.emptyTitle}>No Offers Available</Text>
            <Text style={styles.emptySubtitle}>Please check back later!</Text>
          </View>
          )
        }
      />
    </View>
  )
}

export default OffersPage

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  offerTitle: {
    fontSize: responsiveFontSize(2),
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: responsiveHeight(0.5),
  },
  description: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
    marginBottom: responsiveHeight(1),
  },
  row: {
    flexDirection: 'row',
    marginBottom: responsiveHeight(0.5),
  },
  label: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.tertiary,
    fontWeight: '600',
    width: responsiveWidth(28),
  },
  value: {
    fontSize: responsiveFontSize(1.7),
    color: Colors.black,
  },
  code: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.success,
    fontWeight: 'bold',
  },
  statusTag: (status) => ({
    alignSelf: 'flex-start',
    marginTop: responsiveHeight(1),
    paddingHorizontal: responsiveWidth(3),
    paddingVertical: responsiveHeight(0.5),
    borderRadius: responsiveWidth(2),
    backgroundColor:
      status === 'Active' ? Colors.success :
      status === 'Limited Time' ? Colors.warning :
      Colors.grey,
  }),
  statusText: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.white,
    fontWeight: 'bold',
  },
  // Empty List Styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: '600',
    color: Colors.primary,
  },
  emptySubtitle: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
    marginTop: responsiveHeight(1),
  },

  expiredOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    paddingVertical: responsiveHeight(0.7),
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderTopLeftRadius: responsiveWidth(3),
    borderTopRightRadius: responsiveWidth(3),
    alignItems: 'center',
    overflow: 'hidden',      // keeps corners crisp
    zIndex: 5,        // keeps the banner on top
    
  },
  expiredText: {
    color: Colors.white,
    fontSize: responsiveFontSize(1.8),
    fontWeight: 'bold',
  },

  strikeThroughText: {
  textDecorationLine: 'line-through',
  color: Colors.grey,
},

});