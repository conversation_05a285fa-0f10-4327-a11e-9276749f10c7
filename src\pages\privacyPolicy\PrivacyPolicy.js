import {View, Text, StyleSheet, Pressable, ScrollView, useWindowDimensions, ActivityIndicator } from 'react-native';
import React, { useEffect, useState } from 'react';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import RenderHTML from 'react-native-render-html';
import MyHeader from '../../components/MyHeader';

const PrivacyPolicy = ({navigation}) => {
    const { width } = useWindowDimensions();
    const [htmlContent, setHtmlContent] = useState(`
        <p style='text-align:center;'>
        Privacy Policy
        </p>`);
    const [loading, setLoading] = useState(false);

  return (
    <View style={styles.main}>
        <MyHeader title="Privacy Policy" onBackPress={() => navigation.goBack()} />
        <ScrollView style={[styles.mainContent, {}]}> 
        {loading ? (
            <ActivityIndicator size={'small'} color={Colors.primary} style={styles.indicator} />
          ) : (
            <RenderHTML contentWidth={width} source={{ html: htmlContent }} />
        )}
        </ScrollView>
    </View>
  )
}

export default PrivacyPolicy

const styles = StyleSheet.create({
  main: {
    flex: 1,
  },
  mainContent: {
    backgroundColor: 'white',
    paddingHorizontal: responsiveWidth(3)
    // paddingVertical: responsiveHeight(1),
  },
  indicator: {
    marginTop: responsiveHeight(15),
    alignSelf: 'center',
    justifyContent:'center'
  },
  
});