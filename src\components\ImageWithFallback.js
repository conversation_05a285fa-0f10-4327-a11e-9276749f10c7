import React, { useEffect, useState } from 'react';
import { Image, View, StyleSheet, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { responsiveFontSize } from 'react-native-responsive-dimensions';
import Colors from '../styles/Colors';

const ImageWithFallback = ({source, style, iconName = 'account', iconSize = 5, iconColor = Colors.primaryWithOpacity, containerStyle = {},}) => {
    const [hasError, setHasError] = useState(!source?.uri);
    const [loading, setLoading] = useState(!source?.uri);
    // console.log(source)
    useEffect(() => {
      setHasError(!source?.uri);
      setLoading(!!source?.uri);
    }, [source]);

    const handleLoadEnd = () => setLoading(false);
    const handleError = () => {
      setHasError(true);
      setLoading(false);
    };

  return (
     <View style={[styles.container, containerStyle]}>
      {loading && (
        <View style={[StyleSheet.absoluteFill, styles.loader]}>
          <ActivityIndicator size="small" color={Colors.primary} />
        </View>
      )}

      {!hasError ? (
        <Image
          source={source}
          style={style}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
        />
      ) : (
        !loading && (
          <View style={[style, styles.iconContainer]}>
            <Icon 
              name={iconName} 
              size={responsiveFontSize(iconSize)} 
              color={iconColor} 
            />
          </View>
        )
      )}
    </View>
  )
}

export default ImageWithFallback

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.primaryWithExtraOpacity,
  },
  loader: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
});