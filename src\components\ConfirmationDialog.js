import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from 'react-native-responsive-dimensions';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Colors from '../styles/Colors';

const ConfirmationDialog = ({
  visible,
  onClose,
  title = 'Confirm',
  message = 'Are you sure?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  icon = 'alert-circle-outline',
  iconColor = Colors.primary,
  isLoading
}) => {
  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.dialogContainer}>
              <View style={[styles.iconContainer, {backgroundColor: `${iconColor}20`, }]}>
                <Icon name={icon} size={responsiveFontSize(4)} color={iconColor} />
              </View>

              <Text style={styles.title}>{title}</Text>
              <Text style={styles.message}>{message}</Text>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>{cancelText}</Text>
                </TouchableOpacity>
                
                {isLoading ? (
                    <View style={[styles.button, styles.confirmButton, {backgroundColor: iconColor}]}>
                        <ActivityIndicator size="small" color={Colors.white} />
                    </View>
                ) : 
                    <TouchableOpacity
                    style={[styles.button, styles.confirmButton, {backgroundColor: iconColor}]}
                    onPress={() => {
                        onConfirm();
                        onClose();
                    }}
                    >
                    <Text style={styles.confirmButtonText}>{confirmText}</Text>
                    </TouchableOpacity>
                }
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: responsiveWidth(80),
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(5),
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: responsiveWidth(16),
    height: responsiveWidth(16),
    borderRadius: responsiveWidth(8),
    // backgroundColor: Colors.primaryWithExtraOpacity,
    backgroundColor: `${Colors.primary}20`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: responsiveHeight(2),
  },
  title: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: responsiveHeight(1),
    textAlign: 'center',
  },
  message: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
    marginBottom: responsiveHeight(2),
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    paddingVertical: responsiveHeight(1.2),
    paddingHorizontal: responsiveWidth(4),
    borderRadius: responsiveWidth(2),
    minWidth: responsiveWidth(30),
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: Colors.lightGray,
    marginRight: responsiveWidth(2),
  },
  confirmButton: {
    backgroundColor: Colors.primary,
  },
  cancelButtonText: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.tertiary,
  },
  confirmButtonText: {
    fontSize: responsiveFontSize(1.6),
    fontWeight: '500',
    color: Colors.white,
  },
});

export default ConfirmationDialog;